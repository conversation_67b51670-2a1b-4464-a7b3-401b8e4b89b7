import 'react-native-gesture-handler';
import * as React from 'react';
import { Navigation } from './src/navigation/Navigation';
import { Provider as PaperProvider } from 'react-native-paper';
import { AuthProvider } from './src/contexts/AuthContext';
import { TokenLoadBuffer } from './src/components/TokenLoadBuffer';
import 'intl';
import 'intl/locale-data/jsonp/en-US';
import 'intl/locale-data/jsonp/cs-CZ';
import { theme } from './src/constants/theme';
import { LocalesProvider } from './src/contexts/LocalesContext';
import { UserFiltersProvider } from './src/contexts/UserFiltersContext';

export default function App() {
	return (
		<LocalesProvider>
			<PaperProvider theme={theme}>
				<AuthProvider>
					<UserFiltersProvider>
						<TokenLoadBuffer>
							<Navigation />
						</TokenLoadBuffer>
					</UserFiltersProvider>
				</AuthProvider>
			</PaperProvider>
		</LocalesProvider>
	);
}
