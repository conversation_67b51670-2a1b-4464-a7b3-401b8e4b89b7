import 'react-native-gesture-handler';
import { useEffect } from 'react';
import { Stack } from 'expo-router';
import { Provider as PaperProvider } from 'react-native-paper';
import * as SplashScreen from 'expo-splash-screen';
import { AuthProvider } from '../src/contexts/AuthContext';
import { LocalesProvider } from '../src/contexts/LocalesContext';
import { UserFiltersProvider } from '../src/contexts/UserFiltersContext';
import { theme } from '../src/constants/theme';
import 'intl';
import 'intl/locale-data/jsonp/en-US';
import 'intl/locale-data/jsonp/cs-CZ';

// Prevent the splash screen from auto-hiding before asset loading is complete
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  useEffect(() => {
    // Hide splash screen after app is ready
    SplashScreen.hideAsync();
  }, []);

  return (
    <LocalesProvider>
      <PaperProvider theme={theme}>
        <AuthProvider>
          <UserFiltersProvider>
            <Stack
              screenOptions={{
                headerShown: false,
              }}
            >
              <Stack.Screen name="(auth)" options={{ headerShown: false }} />
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="(modal)" options={{ presentation: 'modal' }} />
            </Stack>
          </UserFiltersProvider>
        </AuthProvider>
      </PaperProvider>
    </LocalesProvider>
  );
}
