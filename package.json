{"name": "visidoo", "version": "1.0.4", "main": "node_modules/expo/AppEntry.js", "engines": {"node": ">=16.13.0", "npm": ">=8.1.0"}, "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "compile:i18n": "formatjs compile-folder --ast src/i18n/messages/translated src/i18n/messages/compiled --format src/i18n/utils/compile.js", "download-translations": "node ./src/i18n/utils/download.mjs", "extract:i18n": "formatjs extract \"src/**/*.ts*\" --ignore=\"**/*.d.ts\" --id-interpolation-pattern \"[sha512:contenthash:base64:6]\" --out-file src/i18n/messages/extracted/cs.json --format src/i18n/utils/format.js", "postdownload-translations": "npm run compile:i18n", "prebuild": "npm run transpile-locales", "prestart": "npm run transpile-locales", "preupload-translations": "npm run extract:i18n", "transpile-locales": "tsc --p tsconfig-locales.json", "upload-translations": "node ./src/i18n/utils/upload.mjs"}, "dependencies": {"@react-navigation/drawer": "^6.4.1", "@react-navigation/native": "^6.0.10", "@react-navigation/native-stack": "^6.6.2", "expo": "~44.0.0", "expo-app-loading": "~1.3.0", "expo-notifications": "~0.14.0", "expo-secure-store": "~11.1.0", "expo-status-bar": "~1.2.0", "expo-task-manager": "~10.1.0", "expo-updates": "~0.11.7", "intl": "^1.2.5", "react": "17.0.1", "react-dom": "17.0.1", "react-intl": "^6.0.3", "react-native": "0.64.3", "react-native-gesture-handler": "~2.1.0", "react-native-paper": "^4.12.1", "react-native-reanimated": "^2.3.3", "react-native-safe-area-context": "3.3.2", "react-native-screens": "~3.10.1", "react-native-svg": "12.1.1", "react-native-web": "0.17.1", "react-native-webview": "11.15.0", "react-navigation-stack": "^2.10.4", "styled-components": "^5.3.5"}, "devDependencies": {"@babel/core": "^7.12.9", "@formatjs/cli": "^5.0.1", "@superkoders/prettier-config": "^0.2.6", "@types/react": "~17.0.21", "@types/react-native": "~0.64.12", "@types/styled-components-react-native": "^5.1.3", "babel-plugin-formatjs": "^10.3.15", "dotenv": "^16.0.1", "node-fetch": "^3.2.4", "prettier": "2.6.2", "react-native-svg-transformer": "^1.0.0", "typescript": "~4.3.5"}, "private": true}