{"name": "visidoo", "version": "1.0.4", "main": "node_modules/expo/AppEntry.js", "engines": {"node": ">=18.0.0", "npm": ">=8.1.0"}, "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "compile:i18n": "formatjs compile-folder --ast src/i18n/messages/translated src/i18n/messages/compiled --format src/i18n/utils/compile.js", "download-translations": "node ./src/i18n/utils/download.mjs", "extract:i18n": "formatjs extract \"src/**/*.ts*\" --ignore=\"**/*.d.ts\" --id-interpolation-pattern \"[sha512:contenthash:base64:6]\" --out-file src/i18n/messages/extracted/cs.json --format src/i18n/utils/format.js", "postdownload-translations": "npm run compile:i18n", "prebuild": "npm run transpile-locales", "prestart": "npm run transpile-locales", "preupload-translations": "npm run extract:i18n", "transpile-locales": "tsc --p tsconfig-locales.json", "upload-translations": "node ./src/i18n/utils/upload.mjs"}, "dependencies": {"@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "expo": "^54.0.2", "expo-splash-screen": "~0.29.0", "expo-notifications": "~0.30.0", "expo-secure-store": "~13.0.2", "expo-status-bar": "~2.0.0", "expo-task-manager": "~12.0.0", "expo-updates": "~0.26.0", "intl": "^1.2.5", "react": "19.1.0", "react-dom": "19.1.0", "react-intl": "^6.0.3", "react-native": "0.81.0", "react-native-gesture-handler": "~2.22.0", "react-native-paper": "^5.12.5", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.14.0", "react-native-screens": "~4.1.0", "react-native-svg": "15.9.0", "react-native-web": "0.21.0", "react-native-webview": "13.12.2", "styled-components": "^5.3.5"}, "devDependencies": {"@babel/core": "^7.25.0", "@formatjs/cli": "^6.2.15", "@superkoders/prettier-config": "^0.2.6", "@types/react": "~19.0.0", "@types/react-native": "~0.81.0", "@types/styled-components-react-native": "^5.2.5", "babel-plugin-formatjs": "^10.5.18", "dotenv": "^16.4.5", "node-fetch": "^3.3.2", "prettier": "3.3.3", "react-native-svg-transformer": "^1.5.0", "typescript": "~5.6.0"}, "private": true}