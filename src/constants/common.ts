import { Dimensions } from 'react-native';

// COLORS
export const PRIMARY_COLOR = '#f58000';
export const SECONDARY_COLOR = '#5620ff';
export const LIGHT_GREY = '#E5E7E9';
export const MEDIUM_GREY = '#7b7682';
export const DARK_GREY = '#424949';
export const WHITE = '#FFFFFF';
export const MINT = '#82E0AA';
export const GREEN = '#28B463';

// MEASUREMENTS
const FIFTEEN = '15px';
export const BORDER_RADIUS = '5px';
export const MEDIUM_FONT_SIZE = FIFTEEN;
export const SPACING = FIFTEEN;
export const SCREEN_WIDTH = Dimensions.get('window').width;
export const SCREEN_HEIGHT = Dimensions.get('window').height;

// API
export const API_BASE_URL = 'https://api.visidoo.com';
export const LOGIN_ENDPOINT = '/auth/login';
export const LOGOUT_ENDPOINT = '/auth/logout';
export const OFFERS_ENDPOINT = '/user/real-estate';
export const FILTERS_ENDPOINT = '/filter';

// WEB
export const WEB_BASE_URL = 'https://www.visidoo.cz';

// ERROR
export const GENERIC_ERROR = 'Something went wrong';
export const ERROR_PUSHNOTI_NOT_GRANTED = `Push notifications aren't granted`;

// TIME
export const SECOND = 1000;
