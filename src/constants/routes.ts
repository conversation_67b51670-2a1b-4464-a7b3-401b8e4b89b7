import { IntlShape } from 'react-intl';
import { Offer } from '../types/offer';

type Route = {
	name: keyof RouteParamsList;
	title: string;
};

type RouteName = 'dashboardRoute' | 'offerDetailsRoute' | 'sourceOfferRoute' | 'newFilterRoute' | 'myFiltersRoute' | 'editProfileRoute';

export const APP_ROUTES: Record<RouteName, Route> = {
	dashboardRoute: {
		name: 'DashboardScreen',
		title: 'Dashboard',
	},
	offerDetailsRoute: {
		name: 'OfferDetailsScreen',
		title: 'Detail nabídky',
	},
	sourceOfferRoute: {
		name: 'SourceOfferScreen',
		title: 'Kontaktovat prodejce',
	},
	newFilterRoute: {
		name: 'NewFilterScreen',
		title: 'Přidat nový filter',
	},
	myFiltersRoute: {
		name: 'MyFiltersScreen',
		title: '<PERSON><PERSON> filtry',
	},
	editProfileRoute: {
		name: 'EditProfileScreen',
		title: 'Upravit profil',
	},
};

export type RouteParamsList = {
	DashboardScreen: undefined;
	MyFiltersScreen: undefined;
	NewFilterScreen: undefined;
	EditProfileScreen: undefined;
	OfferDetailsScreen: { offer: Offer };
	SourceOfferScreen: { offerUrl: string; offerTitle: string };
};

export const createLocalisedRouteLabel = (routeName: string, intl: IntlShape) => {
	switch (routeName) {
		case APP_ROUTES.newFilterRoute.title:
			return intl.formatMessage({ description: 'myFilters-addFilter', defaultMessage: 'Přidat nový filtr' });

		case APP_ROUTES.myFiltersRoute.title:
			return intl.formatMessage({ description: 'mobile: myFilters', defaultMessage: 'Moje filtry' });

		case APP_ROUTES.editProfileRoute.title:
			return intl.formatMessage({ description: 'mobile: editProfile', defaultMessage: 'Upravit profil' });

		case APP_ROUTES.offerDetailsRoute.title:
			return intl.formatMessage({ description: 'mobile: offerDetail', defaultMessage: 'Detail nabídky' });

		default:
			return routeName;
	}
};
