import { DefaultTheme } from 'react-native-paper';

import { Theme } from 'react-native-paper/lib/typescript/types';
import 'intl';
import 'intl/locale-data/jsonp/en-US';
import 'intl/locale-data/jsonp/cs-CZ';
import { PRIMARY_COLOR, SECONDARY_COLOR, WHITE } from './common';

export const theme: Theme = {
	...DefaultTheme,
	roundness: 5,
	colors: {
		...DefaultTheme.colors,
		primary: PRIMARY_COLOR,
		accent: SECONDARY_COLOR,
		background: WHITE,
	},
};
