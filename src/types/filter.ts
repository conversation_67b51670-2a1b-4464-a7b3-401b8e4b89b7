import { OfferType } from './offer';
import { DateRange } from './time';
import { UserState } from './user';

export const PRICE_HIGH_SALE = 100000000;
export const PRICE_HIGH_RENT = 50000;
export const AREA_HIGH_DEFAULT = 1000;
export const LAND_AREA_HIGH_DEFAULT = 10000;

export enum RealEstateType {
	APARTMENT = 'Apartment',
	HOUSE = 'House',
	LAND = 'Land',
	COMMERCIAL = 'Commercial',
	OTHER = 'Other',
}

export enum LandType {
	BUILD_PLOT = 'BuildPlot',
	COMMERCIAL = 'Commercial',
	OTHER = 'Other',
}

export enum FilterState {
	ACTIVE = 'Active',
	DELETED = 'Deleted', // Not used at the moment
	DISABLED = 'Disabled',
}

export enum FilterRelation {
	NONE = 'None',
	EXACT_MATCH = 'ExactMatch',
	WITHIN = 'Within',
	CONTAINS = 'Contains',
}

export type FilterRange = { gte: number; lte: number | null };

export type Location = {
	id: string;
	area: number;
	name: string;
	fullName: string;
	part: string | null;
	city: string | null;
	district: string | null;
	region: string | null;
	cohesion: string | null;
	country: string | null;
};

export type EstimateRequest = Omit<FilterRequest, 'email' | 'state'>;

export type FilterFormValues = {
	adType: OfferType;
	realEstateType: RealEstateType;
	locationId: string;
	email: string;
	exactMatch: boolean;
	radius: number;
	disposition: string[];
	priceLow: number;
	priceHigh: number;
	areaLow: number;
	areaHigh: number;
	landAreaLow: number;
	landAreaHigh: number;
	landType: LandType[];
	state: FilterState;
};

export type FilterRequest = {
	adType: OfferType;
	realEstateType: RealEstateType;
	locationId: string;
	email: string;
	exactMatch: boolean;
	radius: number;
	disposition: string[];
	price: FilterRange;
	area: FilterRange;
	landArea: FilterRange;
	landType: LandType[];
	state: FilterState;
};

export type EstimateData = {
	estimate: Estimate;
};

export type Estimate = {
	range: DateRange;
	value: number;
};

export type Filter = {
	id: string;
	adType: OfferType;
	createdAt: number;
	updatedAt: number;
	realEstateType: RealEstateType;
	locationId?: string;
	location: Location;
	radius: number;
	disposition: string[];
	price: FilterRange;
	area: FilterRange;
	landArea: FilterRange;
	landType: LandType[];
	email: string;
	exactMatch: boolean;
	notificationsSent: number;
	estimate: Estimate;
	state: FilterState;
};

export type FilterResponse = {
	filter: Filter;
	relation?: FilterRelation;
	user?: {
		state?: UserState;
		activeFilters?: number;
	};
};

export type FiltersResponse = {
	filters: Filter[];
};

export type FilterDeleteResponse = {
	user: {
		activeFilters: number;
	};
};

export type ApiResponse<T> = {
	statusCode: number;
	message: string;
	data: T;
};

export type AutocompleteData = {
	suggestions: Location[];
};

export type GeoData = {
	geo: Location;
};

export type FilterOption = {
	label: string;
	value: string | null;
};
