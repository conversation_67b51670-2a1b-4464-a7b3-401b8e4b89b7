import { LandType, RealEstateType, Location } from './filter';

export enum OfferType {
	SALE = 'Sale',
	RENT = 'Rent',
	AUCTION = 'Auction',
	OTHER = 'Other',
	ROOMMATES = 'Roommates',
}

export type OfferResponse = {
	realEstates: Offer[];
};

export type Offer = {
	adType: OfferType;
	area: number;
	createdAt: number;
	disposition: string;
	id: string;
	imageUrl: string;
	landArea: number;
	landType: LandType;
	location?: Location;
	origin: string;
	price: number;
	priceNote: string;
	priceText: string;
	realEstateType: RealEstateType;
	url: string;
	filterId: string;
};
