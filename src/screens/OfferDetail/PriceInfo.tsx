import { FontAwesome5 } from '@expo/vector-icons';
import { FC } from 'react';
import { useIntl } from 'react-intl';
import { Text } from 'react-native-paper';
import styled from 'styled-components/native';
import { LightParagraph } from '../../components/UI/Typography';
import { MINT } from '../../constants/common';
import { Offer } from '../../types/offer';
import { createOfferPrice, createOfferPricePerSqm } from '../../utils/createOfferPrice';

type Props = {
	offer: Offer;
};

export const PriceInfo: FC<Props> = ({ offer }) => {
	const intl = useIntl();
	const priceFormatted = createOfferPrice(offer, intl);
	const pricePerSqm = createOfferPricePerSqm(offer);

	return (
		<StyledPriceInfoWrapper>
			<FontAwesome5 name="coins" size={15} color={MINT} />
			<PriceTextElementsWrapper>
				<StyledPrice>{priceFormatted}</StyledPrice>
				{pricePerSqm ? <LightParagraph>({pricePerSqm})</LightParagraph> : null}
			</PriceTextElementsWrapper>
		</StyledPriceInfoWrapper>
	);
};

const StyledPriceInfoWrapper = styled.View`
	display: flex;
	flex-direction: row;
	align-items: center;
`;

const PriceTextElementsWrapper = styled.View`
	margin-left: 10px;
`;

const StyledPrice = styled(Text)`
	font-size: 18px;
	margin-bottom: 2px;
`;
