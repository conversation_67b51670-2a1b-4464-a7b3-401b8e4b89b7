import React, { FC } from 'react';
import { Card, Title } from 'react-native-paper';
import styled from 'styled-components/native';
import { BORDER_RADIUS, PRIMARY_COLOR } from '../../constants/common';
import { PriceInfo } from './PriceInfo';
import { ScreenLayout } from '../../components/UI/ScreenLayouts';
import { StyledCard } from '../../components/UI/StyledCard';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { createOfferLocation } from '../../utils/createOfferLocation';
import { Tags } from './Tags';
import { RouteParamsList } from '../../constants/routes';
import { MainButton } from '../../components/Button';
import { LightParagraph } from '../../components/UI/Typography';
import { FormattedMessage, useIntl } from 'react-intl';
import { commonInltMessages } from '../../i18n/commonMessages';
import { useOfferDetails } from '../../hooks/useOfferDetails';
type ScreenProps = NativeStackScreenProps<RouteParamsList, 'OfferDetailsScreen'>;

const { notSpecifiedLabel } = commonInltMessages;

// TODO: Implement image fallback!

export const OfferDetailScreen: FC<ScreenProps> = ({ navigation, route }) => {
	const { offer } = route.params;
	const location = offer.location ? createOfferLocation({ location: offer.location }) : <FormattedMessage {...notSpecifiedLabel} />;
	const { formatMessage } = useIntl();
	const { title, date, hour } = useOfferDetails(offer);

	const goToSouceOffer = () => {
		navigation.navigate('SourceOfferScreen', { offerUrl: offer.url, offerTitle: title });
	};

	return (
		<ScreenLayout variant="flexStart">
			<StyledCard onPress={goToSouceOffer}>
				<StyledCardCover
					source={{ uri: offer.imageUrl }}
					onError={(error) => {
						error.persist();
						console.error(error.nativeEvent.error);
					}}
				/>
				<StyledCardTitle>{title}</StyledCardTitle>
				<Location>{location}</Location>
				<BottomWrapper>
					<PriceInfo offer={offer} />
					<Tags offer={offer} offerTime={`${date} ${hour}`} />
				</BottomWrapper>
			</StyledCard>
			<MainButton
				text={formatMessage({
					defaultMessage: 'Kontaktovat prodejce',
					description: 'mobile: contactSeller',
				})}
				onPress={goToSouceOffer}
			/>
		</ScreenLayout>
	);
};

const StyledCardCover = styled(Card.Cover)`
	border-radius: ${BORDER_RADIUS};
	width: 100%;
`;

const StyledCardTitle = styled(Title)`
	color: ${PRIMARY_COLOR};
	font-size: 19px;
	line-height: 25px;
	margin-top: 10px;
	margin-bottom: 5px;
	text-decoration: underline;
	text-decoration-color: ${PRIMARY_COLOR};
	font-weight: bold;
`;

const Location = styled(LightParagraph)`
	font-size: 16px;
`;

const BottomWrapper = styled.View`
	justify-content: space-between;
	margin-top: 20px;
	width: 100%;
	display: flex;
	flex-direction: row;
	align-items: center;
`;

const StyledPriceInfo = styled(PriceInfo)`
	flex: 1 1 auto;
	margin-right: 10px;
`;

const StyledTags = styled(Tags)`
	flex: 0 0 auto;
`;
