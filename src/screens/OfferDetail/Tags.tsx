import { AntDesign } from '@expo/vector-icons';
import { FC } from 'react';
import { View } from 'react-native';
import styled from 'styled-components/native';
import { DARK_GREY, LIGHT_GREY, MEDIUM_FONT_SIZE, SECONDARY_COLOR, WHITE } from '../../constants/common';
import { Offer } from '../../types/offer';

type Props = {
	offer: Offer;
	offerTime: string;
};

export const Tags: FC<Props> = ({ offer, offerTime }) => {
	return (
		<View>
			<AgencyTag agency={offer.origin} />
			<TimeTag formattedTime={offerTime} />
		</View>
	);
};

type AgencyTagProps = {
	agency: string;
};

export const AgencyTag: FC<AgencyTagProps> = ({ agency }) => {
	return (
		<LightTag>
			<AntDesign name="pushpino" size={12} color={DARK_GREY} style={{ marginRight: 8 }} />
			<LightTagText>{agency}</LightTagText>
		</LightTag>
	);
};

type TimeTagProps = {
	formattedTime: string;
};

export const TimeTag: FC<TimeTagProps> = ({ formattedTime }) => {
	return (
		<DarkTag>
			<AntDesign name="clockcircleo" size={12} color={WHITE} style={{ marginRight: 8 }} />
			<DarkTagText>{formattedTime}</DarkTagText>
		</DarkTag>
	);
};

const StyledTag = styled.View`
	padding: 5px 8px;
	/* margin-bottom: 10px; */
	border-radius: 2px;
	align-items: center;
	justify-content: center;
	flex-direction: row;
`;

const LightTag = styled(StyledTag)`
	background: ${LIGHT_GREY};
	margin-bottom: 10px;
`;

const DarkTag = styled(StyledTag)`
	background: ${SECONDARY_COLOR};
`;

const BaseTagText = styled.Text`
	font-size: ${MEDIUM_FONT_SIZE};
`;

const DarkTagText = styled(BaseTagText)`
	color: ${WHITE};
`;

const LightTagText = styled(BaseTagText)`
	color: ${DARK_GREY};
	font-weight: 500;
`;
