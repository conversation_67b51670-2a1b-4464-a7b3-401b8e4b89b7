import { NativeStackScreenProps } from '@react-navigation/native-stack';
import React, { FC, useContext, useEffect } from 'react';
import { WebBridge } from '../../components/WebBridge';
import { APP_ROUTES, RouteParamsList } from '../../constants/routes';
import { UserFiltersContext } from '../../contexts/UserFiltersContext';
import { useLocalizedUri } from '../../hooks/useLocalizedUri';

const { myFiltersRoute } = APP_ROUTES;

type ScreenProps = NativeStackScreenProps<RouteParamsList, 'NewFilterScreen'>;

export const NewFilterScreen: FC<ScreenProps> = ({ navigation }) => {
	const localizedUri = useLocalizedUri('/webview/novy-filtr');
	const { refreshUserFilters } = useContext(UserFiltersContext);

	const handleMessage = (message: string) => {
		if (message === 'ADDFILTER_DONE') {
			navigation.navigate(myFiltersRoute.name);
		}
	};

	useEffect(() => {
		navigation.addListener('beforeRemove', refreshUserFilters);
		return () => navigation.removeListener('beforeRemove', refreshUserFilters);
	}, [navigation, refreshUserFilters]);

	return <WebBridge source={{ uri: localizedUri }} onMessage={handleMessage} />;
};
