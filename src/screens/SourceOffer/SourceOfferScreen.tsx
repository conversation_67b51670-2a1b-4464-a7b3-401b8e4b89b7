import { FC, useEffect } from 'react';
import { WebView } from 'react-native-webview';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RouteParamsList } from '../../constants/routes';

type ScreenProps = NativeStackScreenProps<RouteParamsList, 'SourceOfferScreen'>;

export const SourceOfferScreen: FC<ScreenProps> = ({ route, navigation }) => {
	const { offerUrl, offerTitle } = route.params;

	useEffect(() => {
		navigation.setOptions({
			title: offerTitle,
		});
	}, [navigation, offerTitle]);

	return <WebView source={{ uri: offerUrl }} />;
};
