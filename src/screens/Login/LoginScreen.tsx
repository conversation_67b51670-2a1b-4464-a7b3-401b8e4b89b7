import { useContext, useState, FC } from 'react';
import { TextInput, HelperText } from 'react-native-paper';
import styled from 'styled-components/native';
import { MainButton } from '../../components/Button';
import { KeyboardAvoider } from '../../components/KeyboardAvoider';
import { API_BASE_URL, LOGIN_ENDPOINT, MEDIUM_GREY } from '../../constants/common';
import { AuthContext } from '../../contexts/AuthContext';
import { LoginInputs, AuthResponse } from '../../types/auth';
import { registerForPushNotifications } from '../../utils/notifications/registerForPushNotifications';
import { savePushNotificationToken } from '../../utils/notifications/tokenStorage';
import { Logo } from '../../components/Logo';
import { ScreenLayout } from '../../components/UI/ScreenLayouts';
import { Platform } from 'react-native';
import { FormattedMessage, useIntl } from 'react-intl';
import { LanguagePicker } from '../../components/LanguagePicker';
import { useIsKeyboardOpen } from '../../hooks/useIsKeyboardOpen';

export const LoginScreen: FC = () => {
	const { saveToken } = useContext(AuthContext);
	const [email, setEmail] = useState('');
	const [password, setPassword] = useState('');
	const [error, setError] = useState(false);
	const [loading, setLoading] = useState(false);
	const [isShowingPassword, setIsShowingPassword] = useState(false);

	const { formatMessage } = useIntl();
	const { keyboardIsShowing } = useIsKeyboardOpen();

	const togglePasswordVisibility = () => setIsShowingPassword((prev) => !prev);

	const tryLogin = async (input: LoginInputs) => {
		try {
			setLoading(true);
			let pushNotitficationToken;
			try {
				pushNotitficationToken = await registerForPushNotifications();
				console.debug(pushNotitficationToken); // TODO: remove
			} catch (error) {
				// TODO: log error
			}
			const url = `${API_BASE_URL}${LOGIN_ENDPOINT}`;
			const res = await fetch(url, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(pushNotitficationToken ? { ...input, notificationToken: pushNotitficationToken } : input),
			});

			if (res.status !== 200) {
				setError(true);
				setLoading(false);
				return;
			}

			const authParsedResponse: AuthResponse = await res.json();
			await Promise.all([
				saveToken(authParsedResponse.data.token),
				pushNotitficationToken && savePushNotificationToken(pushNotitficationToken),
			]);
		} catch (error) {
			console.error(error);
			setError(true);
		}
	};

	const footerSection = (
		<FooterSection>
			{/* TODO: Temporary hack to fix keyboardAvoiding view issues in Android */}
			{Platform.OS === 'android' && keyboardIsShowing ? null : <LanguagePicker />}
			<CopyRight>
				© {new Date().getFullYear()} Visidoo •{' '}
				<FormattedMessage defaultMessage="Všechna práva vyhrazena." description="mobile: rightsReserved" />
			</CopyRight>
		</FooterSection>
	);

	return (
		<ScreenLayout>
			<KeyboardAvoider>
				<>
					<LogoContainer>
						<Logo variant="large" />
					</LogoContainer>
					<InputsContainer>
						<Input
							label="E-mail"
							value={email}
							onChangeText={(text) => setEmail(text)}
							autoComplete={false}
							autoCapitalize="none"
							keyboardType="email-address"
							error={error}
							onFocus={() => setError(false)}
						/>
						<Input
							label={formatMessage({ defaultMessage: 'Heslo', description: 'registrationForm-password' })}
							value={password}
							onChangeText={(text) => setPassword(text)}
							autoComplete={false}
							autoCapitalize="none"
							secureTextEntry={!isShowingPassword}
							error={error}
							onFocus={() => setError(false)}
							right={
								<TextInput.Icon
									name={isShowingPassword ? 'eye-off' : 'eye'}
									onPress={togglePasswordVisibility}
									color={MEDIUM_GREY}
								/>
							}
						/>
						{/* @ts-ignore TODO: investigate why it gives type error */}
						<HelperText type="error" visible={!!error}>
							Špatný email nebo heslo!
						</HelperText>
						<MainButton
							onPress={() =>
								tryLogin({
									email,
									password,
								})
							}
							text={formatMessage({ defaultMessage: 'Přihlásit se', description: 'userAuthWidget-login' })}
							loading={loading}
						/>
						{/* <BoldText>Zapomenuté heslo?</BoldText> */}
					</InputsContainer>
					{Platform.OS === 'android' && footerSection}
				</>
			</KeyboardAvoider>
			{Platform.OS === 'ios' && footerSection}
		</ScreenLayout>
	);
};

const LogoContainer = styled.View`
	align-items: center;
	justify-content: center;
	flex: 2;
`;

const InputsContainer = styled.View`
	flex: 3;
`;

const Input = styled(TextInput)`
	width: 300px;
	background-color: white;
	margin: 5px 0;
`;

// const BoldText = styled.Text`
// 	font-weight: 600;
// 	align-self: center;
// `;

const FooterSection = styled.View`
	padding-bottom: 20px;
`;

const CopyRight = styled.Text`
	/* padding-bottom: 20px; */
`;
