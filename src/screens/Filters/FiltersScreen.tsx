import { NativeStackScreenProps } from '@react-navigation/native-stack';
import React, { FC, useContext, useEffect } from 'react';
import { WebBridge } from '../../components/WebBridge';
import { RouteParamsList } from '../../constants/routes';
import { UserFiltersContext } from '../../contexts/UserFiltersContext';
import { useLocalizedUri } from '../../hooks/useLocalizedUri';

type ScreenProps = NativeStackScreenProps<RouteParamsList, 'MyFiltersScreen'>;

export const FiltersScreen: FC<ScreenProps> = ({ navigation }) => {
	const localizedUri = useLocalizedUri('/webview/moje-filtry');
	const { refreshUserFilters } = useContext(UserFiltersContext);

	useEffect(() => {
		navigation.addListener('beforeRemove', refreshUserFilters);
		return () => navigation.removeListener('beforeRemove', refreshUserFilters);
	}, [navigation, refreshUserFilters]);

	return <WebBridge source={{ uri: localizedUri }} />;
};
