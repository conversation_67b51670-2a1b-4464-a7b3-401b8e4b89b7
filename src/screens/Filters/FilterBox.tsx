import { FC } from 'react';
import { Text, Card } from 'react-native-paper';
import { Filter } from '../../types/filter';

type Props = {
	filter: Filter;
};

export const FilterBox: FC<Props> = ({ filter }) => {
	const {
		id,
		area,
		landArea,
		landType,
		price,
		createdAt,
		adType,
		realEstateType,
		location,
		disposition,
		exactMatch,
		notificationsSent,
		estimate,
		state,
	} = filter;

	return (
		<Card>
			<Card.Content>
				<Text>{id}</Text>
			</Card.Content>
		</Card>
	);
};
