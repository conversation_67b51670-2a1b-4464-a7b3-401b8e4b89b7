import { FC } from 'react';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { FloatingButton } from '../../components/UI/FloatingButton';
import { WHITE } from '../../constants/common';

type Props = {
	goToNewFilterScreen: () => void;
};

export const PlusButton: FC<Props> = ({ goToNewFilterScreen }) => {
	return (
		<FloatingButton onPress={goToNewFilterScreen}>
			<MaterialCommunityIcons name="plus" size={35} color={WHITE} />
		</FloatingButton>
	);
};
