import { FC } from 'react';
import styled from 'styled-components/native';
import { SPACING, SECONDARY_COLOR } from '../../constants/common';
import { RealEstateType } from '../../types/filter';
import ApartmentSVG from '../../../assets/icons/flat.svg';
import HouseSVG from '../../../assets/icons/house.svg';
import LandSVG from '../../../assets/icons/signpost.svg';

const iconsMap: Record<Partial<RealEstateType>, FC | null> = {
	House: HouseSVG,
	Apartment: ApartmentSVG,
	Land: LandSVG,
	Commercial: null,
	Other: null,
};

type Props = {
	realEstateType: RealEstateType;
};

export const PropertyIcon: FC<Props> = ({ realEstateType }) => {
	const IconToUse = iconsMap[realEstateType];

	return <IconWrapper>{IconToUse && <IconToUse />}</IconWrapper>;
};

const IconWrapper = styled.View`
	width: 50px;
	height: 50px;
	border-radius: 50px;
	background-color: ${SECONDARY_COLOR};
	justify-content: center;
	align-items: center;
	flex: 0 0 auto;
	margin-right: ${SPACING};
`;
