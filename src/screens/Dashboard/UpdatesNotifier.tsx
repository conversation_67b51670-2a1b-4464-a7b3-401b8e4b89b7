import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import { Button } from 'react-native-paper';
import styled from 'styled-components/native';
import { BORDER_RADIUS, WHITE, SECONDARY_COLOR, LIGHT_GREY, SECOND } from '../../constants/common';
import { useTimeout } from '../../hooks/useTimeout';

const REMOVE_NOTICE_AFTER = SECOND * 10;

type Props = {
	triggerRefresh: () => void;
	isVisible: boolean;
	removeNotifier: () => void;
};

export const UpdatesNotifier: FC<Props> = ({ triggerRefresh, isVisible, removeNotifier }) => {
	useTimeout(removeNotifier, REMOVE_NOTICE_AFTER);

	return isVisible ? (
		<Wrapper
			style={{
				shadowColor: '#000',
				shadowOffset: {
					width: 0,
					height: 1,
				},
				shadowOpacity: 0.18,
				shadowRadius: 1.0,
				elevation: 1,
			}}
		>
			<StyledText>
				<FormattedMessage defaultMessage="Máte nové nabídky!" description="mobile: newOffersAvailable" />
			</StyledText>
			<Button onPress={triggerRefresh}>
				<FormattedMessage defaultMessage="Obnovit" description="mobile: refresh" />
			</Button>
		</Wrapper>
	) : null;
};

const Wrapper = styled.View`
	width: 100%;
	background-color: ${WHITE};
	border: 1px solid ${LIGHT_GREY};
	border-radius: ${BORDER_RADIUS};
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	padding: 7px 20px;
	padding-right: 5px;
`;

const StyledText = styled.Text`
	font-weight: 500;
	color: ${SECONDARY_COLOR};
	text-transform: uppercase;
`;
