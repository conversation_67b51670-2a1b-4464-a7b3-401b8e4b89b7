import React, { FC, useCallback, useContext, useEffect, useState } from 'react';
import { FlatList } from 'react-native';
import { Maybe, NavigationProps } from '../../types/common';
import { ScreenLayout } from '../../components/UI/ScreenLayouts';
import { RealEstateOfferCard } from './RealEstateOfferCard';
import { AuthContext } from '../../contexts/AuthContext';
import { SECOND } from '../../constants/common';
import { Offer } from '../../types/offer';
import { Spinner } from '../../components/Spinner';
import { HelperText } from 'react-native-paper';
import { MainButton } from '../../components/Button';
import { useLogout } from '../../utils/useLogout';
import styled from 'styled-components/native';
import { fetchAvailableOffers } from '../../utils/api/fetchAvailableOffers';
import { useFocusEffect } from '@react-navigation/native';
import { APP_ROUTES } from '../../constants/routes';
import { PlusButton } from './PlusButton';
const { newFilterRoute } = APP_ROUTES;
import { OffersFiltering } from '../../components/filtering/OffersFiltering';
import { UpdatesNotifier } from './UpdatesNotifier';
import { Filter } from '../../types/filter';
import { waitFor } from '../../utils/waitFor';
import { UserFiltersContext } from '../../contexts/UserFiltersContext';

const REFRESH_INTERVAL = SECOND * 4;

export const DashboardScreen: FC<NavigationProps> = ({ navigation }) => {
	const [page, setPage] = useState(1);
	const [realEstateOffers, setRealEstateOffers] = useState<Offer[]>([]);
	const [selectedFilter, setSelectedFilter] = useState<Filter | null>(null);
	const [loading, setLoading] = useState(true);
	const [loadingMore, setLoadingMore] = useState(false);
	const [loadedAll, setLoadedAll] = useState(false);
	const [error, setError] = useState<null | number>(null);
	const [isRefreshing, setIsRefreshing] = useState(false);
	const [updatedOffers, setUpdatedOffers] = useState<Offer[] | null>(null);
	const [timeStampLastFetch, setTimeStampLastFetch] = useState<number | null>(null);

	const thereAreUpdates = !!updatedOffers;
	const { logout } = useLogout({ navigation });
	const { token } = useContext(AuthContext);
	const { loading: filtersLoading, userFilters, loadUserFilters } = useContext(UserFiltersContext);

	const goToNewFilterScreen = () => navigation.navigate(newFilterRoute.name);

	const fetchInitialAvailableOffers = useCallback(
		async (filterId: Maybe<string>) => {
			if (!token) {
				return;
			}
			try {
				setError(null);
				setLoading(true);
				const data = await fetchAvailableOffers(token, 1, filterId);
				setRealEstateOffers(data.realEstates);
				setLoading(false);
				// because 7 is magic!
				if (data.realEstates.length < 7) {
					setLoadedAll(true);
				}
			} catch (error) {
				setLoading(false);
				if (!(error instanceof Error)) {
					return setError(520); // 520 just because it is not used in HTTP yet
				}

				if (error.message === '403') {
					await logout();
				}
				setError(Number(error.message));
			}
		},
		[token, fetchAvailableOffers],
	);

	const resetState = useCallback(() => {
		setLoadedAll(false);
		setPage(1);
		setUpdatedOffers(null);
	}, []);

	const onPullDown = useCallback(async () => {
		if (!token) {
			return;
		}
		try {
			setIsRefreshing(true);
			resetState();
			loadUserFilters(token);
			const data = await fetchAvailableOffers(token, 1, selectedFilter?.id);
			const sortedOffers = [...data.realEstates].sort((a, b) => b.createdAt - a.createdAt);
			setRealEstateOffers(sortedOffers);
			setTimeStampLastFetch(Date.now());
		} catch (error) {
			console.error(error);
		} finally {
			setIsRefreshing(false);
		}
	}, [token, resetState, loadUserFilters]);

	const updateOffers = async () => {
		if (!updatedOffers) return;
		setLoading(true);
		resetState();
		setRealEstateOffers(updatedOffers);
		await waitFor(600);
		setLoading(false);
	};

	const renderScreenContent = () => {
		switch (true) {
			case loading:
				return (
					<LoadingAndMessageContainer>
						<Spinner size="large" />
					</LoadingAndMessageContainer>
				);

			case !!error:
				return (
					<LoadingAndMessageContainer>
						{/* @ts-ignore */}
						<HelperText type="error">Něco se pokazilo... Stavový kód: {error}</HelperText>
						<MainButton onPress={() => fetchInitialAvailableOffers(selectedFilter?.id)} text="Try again" />
					</LoadingAndMessageContainer>
				);

			case !selectedFilter && !loading && realEstateOffers.length === 0:
				return (
					<LoadingAndMessageContainer>
						{/* @ts-ignore */}
						<HelperText type="info">Zdá se, že ještě nemáte žádné filtry...</HelperText>
						<MainButton onPress={goToNewFilterScreen} text="Vytvořit filtr" />
					</LoadingAndMessageContainer>
				);

			case selectedFilter && !filtersLoading && !loading && realEstateOffers.length === 0:
				return (
					<>
						{/* TODO: TRANSLATE */}
						<LoadingAndMessageContainer>
							{/* @ts-ignore */}
							<HelperText type="info">No offers available yet for this filter.</HelperText>
						</LoadingAndMessageContainer>
					</>
				);

			default:
				return (
					<>
						<FlatList
							data={realEstateOffers}
							renderItem={({ item }) => <RealEstateOfferCard offer={item} navigation={navigation} />}
							keyExtractor={(item) => item.id}
							style={{ width: '100%', flexGrow: 1, paddingHorizontal: 0 }}
							onRefresh={onPullDown}
							refreshing={isRefreshing}
							onEndReached={loading || loadingMore || loadedAll ? null : () => setPage((page) => page + 1)}
							ListFooterComponent={
								loadingMore ? (
									<LoadingMoreWrapper>
										<Spinner size="small" />
									</LoadingMoreWrapper>
								) : null
							}
						/>
						<PlusButton goToNewFilterScreen={goToNewFilterScreen} />
					</>
				);
		}
	};

	const resetOfferData = (filter: Filter | null) => {
		resetState();
		fetchInitialAvailableOffers(filter?.id);
	};

	const selectFilter = (filter: Filter | null) => {
		// reset timestamp to start fetching again from scratch and avoid false-positive updates
		setTimeStampLastFetch(null);
		setSelectedFilter(filter);
		resetOfferData(filter);
	};

	useFocusEffect(
		React.useCallback(() => {
			switch (true) {
				// avoid multiple consecutive refetches - use 4sec intervals
				case !token ||
					isRefreshing ||
					(typeof timeStampLastFetch === 'number' && timeStampLastFetch + REFRESH_INTERVAL >= Date.now()):
					return;

				// equivalent to onMount useEffect
				case !timeStampLastFetch: {
					fetchInitialAvailableOffers(selectedFilter?.id).then(() => setTimeStampLastFetch(Date.now()));
					break;
				}

				default: {
					// Check for updates
					fetchAvailableOffers(token as string, 1, selectedFilter?.id)
						.then((offersOnServer) => {
							const firstIdOnServer = offersOnServer.realEstates?.[0]?.id;
							const firstIdOnClient = realEstateOffers?.[0]?.id;
							const thereAreUpdates = !!firstIdOnServer && firstIdOnServer !== firstIdOnClient;

							// TODO: Keep the already-fetched offers, instead of just informing the user and refetching them again?
							if (thereAreUpdates) {
								// setThereAreUpdates(true);
								setUpdatedOffers(offersOnServer.realEstates);
							}
						})
						.catch((err) => console.error(err));
				}
			}
		}, [
			fetchInitialAvailableOffers,
			fetchAvailableOffers,
			token,
			selectedFilter?.id,
			realEstateOffers?.[0]?.id,
			isRefreshing,
			timeStampLastFetch,
		]),
	);

	useEffect(() => {
		if (!token || page === 1) {
			return;
		}

		const fetchMore = async () => {
			setLoadingMore(true);
			try {
				const data = await fetchAvailableOffers(token, page, selectedFilter?.id);
				if (data?.realEstates?.length) {
					setRealEstateOffers((offers) => [...offers, ...data.realEstates]);
				} else {
					setLoadedAll(true);
				}
			} catch (error) {
				console.error(error);
			} finally {
				setLoadingMore(false);
			}
		};

		fetchMore();
	}, [token, page]);

	return (
		<ScreenLayout variant="flexStart">
			{thereAreUpdates && (
				<UpdatesNotifier triggerRefresh={updateOffers} isVisible={thereAreUpdates} removeNotifier={() => setUpdatedOffers(null)} />
			)}
			{userFilters.length > 1 && !thereAreUpdates && (
				<OffersFiltering userFilters={userFilters} selectFilter={selectFilter} currentlySelectedFilter={selectedFilter} />
			)}
			{renderScreenContent()}
		</ScreenLayout>
	);
};

const LoadingAndMessageContainer = styled.View`
	flex: 1;
	margin-top: 200px;
`;

const LoadingMoreWrapper = styled.View`
	padding: 10px 0 2px 0;
`;
