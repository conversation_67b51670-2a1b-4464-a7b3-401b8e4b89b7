import { FC, memo } from 'react';
import { Entypo } from '@expo/vector-icons';
import { Caption } from 'react-native-paper';
import { PropertyIcon } from './PropertyIcon';
import { StyledCard } from '../../components/UI/StyledCard';
import styled from 'styled-components/native';
import { Offer } from '../../types/offer';
import { NavigationStackProp } from 'react-navigation-stack';
import { APP_ROUTES } from '../../constants/routes';
import { BoldTitle } from '../../components/UI/Typography';
import { SPACING } from '../../constants/common';
import { useOfferDetails } from '../../hooks/useOfferDetails';

const { offerDetailsRoute } = APP_ROUTES;

type Props = {
	offer: Offer;
	navigation: NavigationStackProp;
};
export const RealEstateOfferCard: FC<Props> = memo(({ offer, navigation }) => {
	const { realEstateType } = offer;
	const { title, date, hour } = useOfferDetails(offer);

	const goToOfferDetail = () => navigation.navigate(offerDetailsRoute.name, { offer });

	return (
		<Card onPress={goToOfferDetail}>
			<CardContentWrapper>
				<PropertyIcon realEstateType={realEstateType} />
				<TextContainer>
					<BoldTitle>{title}</BoldTitle>
					<Caption>
						{date}
						{'  '} {hour}
					</Caption>
				</TextContainer>

				<ArrowContainer>
					<Entypo name="chevron-right" size={24} color="black" />
				</ArrowContainer>
			</CardContentWrapper>
		</Card>
	);
});

const Card = styled(StyledCard)`
	align-self: center;
	margin-bottom: ${SPACING};
	box-shadow: none;
`;

const CardContentWrapper = styled.View`
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
`;

const TextContainer = styled.View`
	flex: 1 1 auto;
`;

const ArrowContainer = styled.View`
	flex: 0 0 auto;
	margin-left: 2px;
`;
