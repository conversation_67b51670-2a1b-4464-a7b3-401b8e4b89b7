import { createNativeStackNavigator, NativeStackNavigationOptions } from '@react-navigation/native-stack';
import React, { useContext } from 'react';
import { DashboardScreen } from '../screens/Dashboard/DashboardScreen';
import { LoginScreen } from '../screens/Login/LoginScreen';
import { OfferDetailScreen } from '../screens/OfferDetail/OfferDetailScreen';
import { AuthContext } from '../contexts/AuthContext';
import { CustomNavigationBar } from '../components/navigation/CustomNavigationBar';
import { APP_ROUTES } from '../constants/routes';
import { SourceOfferScreen } from '../screens/SourceOffer/SourceOfferScreen';
import { NewFilterScreen } from '../screens/NewFilter/NewFilterScreen';
import { FiltersScreen } from '../screens/Filters/FiltersScreen';
import { EditProfileScreen } from '../screens/EditProfile/EditProfileScreen';

// TODO: Fix this any casting
const customBarOptions: NativeStackNavigationOptions = { header: (props) => <CustomNavigationBar {...(props as any)} /> };
const { dashboardRoute, offerDetailsRoute, sourceOfferRoute, newFilterRoute, myFiltersRoute, editProfileRoute } = APP_ROUTES;

const StartingStack = createNativeStackNavigator();
export const Start = () => {
	const { isLoggedIn } = useContext(AuthContext);
	const screenOptions: NativeStackNavigationOptions = isLoggedIn ? customBarOptions : { headerShown: false };

	return (
		<StartingStack.Navigator screenOptions={screenOptions}>
			{isLoggedIn ? (
				<>
					<StartingStack.Screen
						name={dashboardRoute.name}
						component={DashboardScreen}
						options={{ title: dashboardRoute.title }}
					/>
					<StartingStack.Screen
						name={offerDetailsRoute.name}
						component={OfferDetailScreen}
						options={{ title: offerDetailsRoute.title }}
					/>
					<StartingStack.Screen name={sourceOfferRoute.name} component={SourceOfferScreen} options={{ title: '' }} />
					<StartingStack.Screen
						name={newFilterRoute.name}
						component={NewFilterScreen}
						options={{ title: newFilterRoute.title }}
					/>
					<StartingStack.Screen name={myFiltersRoute.name} component={FiltersScreen} options={{ title: myFiltersRoute.title }} />
					<StartingStack.Screen
						name={editProfileRoute.name}
						component={EditProfileScreen}
						options={{ title: editProfileRoute.title }}
					/>
				</>
			) : (
				<StartingStack.Screen name="SignInScreen" component={LoginScreen} />
			)}
		</StartingStack.Navigator>
	);
};
