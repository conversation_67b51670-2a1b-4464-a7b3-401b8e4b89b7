import { createDrawerNavigator } from '@react-navigation/drawer';
import React, { useContext } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { CustomNavigationDrawer } from '../components/navigation/CustomNavigationDrawer';
import { Start } from './Stacks';
import { APP_ROUTES } from '../constants/routes';
import { useOnAppActiveTaskQueue } from '../hooks/useOnAppActiveTaskQueue';
import { UserFiltersContext } from '../contexts/UserFiltersContext';

const { dashboardRoute, newFilterRoute, myFiltersRoute, editProfileRoute } = APP_ROUTES;

const Drawer = createDrawerNavigator();

export const Navigation = () => {
	const { refreshUserFilters } = useContext(UserFiltersContext);

	const tasksToRunWhenAppIsActive = [refreshUserFilters];

	useOnAppActiveTaskQueue(tasksToRunWhenAppIsActive);

	return (
		<NavigationContainer>
			<Drawer.Navigator
				screenOptions={{
					headerShown: false,
					drawerType: 'back',
					drawerStyle: { width: 300 },
				}}
				drawerContent={(props) => <CustomNavigationDrawer {...props} />}
			>
				<Drawer.Screen name={dashboardRoute.title} component={Start} />
				<Drawer.Screen name={newFilterRoute.title} component={Start} />
				<Drawer.Screen name={myFiltersRoute.title} component={Start} />
				<Drawer.Screen name={editProfileRoute.title} component={Start} />
			</Drawer.Navigator>
		</NavigationContainer>
	);
};
