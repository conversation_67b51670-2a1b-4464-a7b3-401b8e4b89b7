import { FC, useContext } from 'react';
import { LocalesContext } from '../contexts/LocalesContext';
import { Locale } from '../i18n/supportedLocales';
import CzFlag from '../../assets/icons/cz.svg';
import EngFlag from '../../assets/icons/en.svg';
import styled from 'styled-components/native';
import { SPACING } from '../constants/common';
import { TouchableOpacity } from 'react-native-gesture-handler';

type Props = {
	closeDrawer?: () => void;
};

export const LanguagePicker: FC<Props> = ({ closeDrawer }) => {
	const { changeLanguage, currentLanguage } = useContext(LocalesContext);
	const isCzechSelected = currentLanguage === Locale.cs;

	const switchLanguage = () => {
		closeDrawer != null && closeDrawer();
		const targetLanguage = isCzechSelected ? Locale.en : Locale.cs;
		changeLanguage(targetLanguage);
	};

	return (
		<TouchableOpacity onPress={switchLanguage}>
			<IconWrapper>{isCzechSelected ? <EngFlag /> : <CzFlag />}</IconWrapper>
		</TouchableOpacity>
	);
};

const IconWrapper = styled.View`
	width: 30px;
	height: 30px;
	margin: ${SPACING};
	align-self: center;
`;
