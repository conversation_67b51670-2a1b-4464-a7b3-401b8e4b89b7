import React, { useContext, useEffect, useRef, useState } from 'react';
import WebView from 'react-native-webview';
import { WebViewMessageEvent, WebViewSource } from 'react-native-webview/lib/WebViewTypes';
import { AuthContext } from '../contexts/AuthContext';

type Props = {
	source: WebViewSource;
	onMessage?: (message: string) => void;
};

export function WebBridge(props: Props) {
	const { source, onMessage } = props;
	const webViewRef = useRef<WebView>(null);
	const { token } = useContext(AuthContext);
	const [webIsReady, setWebIsReady] = useState(false);

	const messageHandler = (event: WebViewMessageEvent) => {
		const { data } = event.nativeEvent;
		if (data === 'READY') {
			setWebIsReady(true);
		} else {
			onMessage?.(data);
		}
	};

	useEffect(() => {
		if (token && webIsReady) {
			webViewRef.current?.injectJavaScript(
				`window.postMessage('{"type":"SETUP", "payload":{"token":"${token}","url":"/moje-filtry?addFilter"}}');`,
			);
		}
	}, [token, webIsReady]);

	return <WebView ref={webViewRef} source={source} onMessage={messageHandler} />;
}

WebBridge.displayName = 'WebBridge';
