import { FC } from 'react';
import { Chip } from 'react-native-paper';
import { SECONDARY_COLOR } from '../../constants/common';

type BaseProps = {
	icon: string;
	onPress: () => void;
	label: string;
};

type Props = {
	showDialog: () => void;
	label: string;
};

export const BaseChip: FC<BaseProps> = ({ onPress, label, icon }) => {
	return (
		<Chip
			icon={icon}
			onPress={onPress}
			mode="outlined"
			selectedColor={SECONDARY_COLOR}
			textStyle={{ fontSize: 14, lineHeight: 18, padding: 0, minHeight: 1 }}
			style={{ justifyContent: 'center' }}
		>
			{label}
		</Chip>
	);
};

export const FilterChip: FC<Props> = ({ showDialog, label }) => {
	return <BaseChip icon="tune" label={label} onPress={showDialog} />;
};
