import { FC, useState } from 'react';
import { <PERSON>ton, Dialog, Portal, Divider, Subheading } from 'react-native-paper';
import styled from 'styled-components/native';
import { SimpleScrollView } from '../../components/UI/SimpleScrollView';
import { Filter, FilterState } from '../../types/filter';
import { SCREEN_HEIGHT, SPACING } from '../../constants/common';
import { JustifiedContent } from '../../components/UI/JustifiedContent';
import { SelectedFilterView } from './SelectedFilterView';
import { FilterOption } from './FilterOption';
import { FilterChip } from './FilterChips';
import { FormattedMessage, useIntl } from 'react-intl';
import { View } from 'react-native';
import { commonInltMessages } from '../../i18n/commonMessages';

type Props = {
	userFilters: Filter[];
	selectFilter: (filter: Filter | null) => void;
	currentlySelectedFilter: Filter | null;
};

export const OffersFiltering: FC<Props> = ({ userFilters, selectFilter, currentlySelectedFilter }) => {
	const [dialogVisible, setDialogVisible] = useState(false);
	const [selectedOption, setSelectedOption] = useState<null | Filter>(currentlySelectedFilter);
	const { formatMessage } = useIntl();
	const { filterActionIntlString } = commonInltMessages;

	const activeFilters = userFilters.filter((filter) => filter.state === FilterState.ACTIVE);
	const disabledFilters = userFilters.filter((filter) => filter.state === FilterState.DISABLED);

	const filterIsActive = !!currentlySelectedFilter;

	const confirmFilterSelection = (filter: Filter | null) => setSelectedOption(filter);

	const showDialog = () => setDialogVisible(true);

	const hideDialog = () => setDialogVisible(false);

	const resetFilters = () => {
		setSelectedOption(null);
		selectFilter(null);
	};

	const onDialogConfirm = () => {
		selectFilter(selectedOption);
		hideDialog();
	};

	const onDialogCancel = () => {
		if (selectedOption?.id !== currentlySelectedFilter?.id) setSelectedOption(null);
		hideDialog();
	};

	return (
		<Wrapper>
			{filterIsActive ? (
				<SelectedFilterView activeFilter={currentlySelectedFilter} showDialog={showDialog} resetFilters={resetFilters} />
			) : (
				<View style={{ paddingBottom: 15, flexDirection: 'row', justifyContent: 'space-between', flexGrow: 1 }}>
					<Subheading>
						<FormattedMessage defaultMessage="Notifikace" description="mobile: notifications" />
					</Subheading>
					<FilterChip showDialog={showDialog} label={formatMessage(filterActionIntlString)} />
				</View>
			)}
			<Portal>
				<StyledDialog visible={dialogVisible} onDismiss={hideDialog}>
					<Dialog.Title>
						<FormattedMessage defaultMessage="Vyberte filtraci" description="mobile: chooseFilter" />
					</Dialog.Title>
					<Dialog.ScrollArea style={{ backgroundColor: '#f9f9f9' }}>
						<SimpleScrollView>
							<FiltersListWrapper>
								{activeFilters.length > 0 && (
									<FilterOption
										filter={null}
										isChecked={selectedOption === null}
										confirmFilterSelection={confirmFilterSelection}
									/>
								)}
								{activeFilters.map((filter) => {
									return (
										<FilterOption
											key={filter.id}
											filter={filter}
											isChecked={filter.id === selectedOption?.id || (!currentlySelectedFilter && !selectedOption)}
											confirmFilterSelection={confirmFilterSelection}
										/>
									);
								})}

								{activeFilters.length > 0 && <Divider style={{ marginTop: 10, marginBottom: 20 }} />}

								<Subheading>
									<FormattedMessage defaultMessage="Vypnuté filtry" description="mobile: disabledFilters" />
								</Subheading>

								{disabledFilters.map((filter) => {
									return (
										<FilterOption
											key={filter.id}
											filter={filter}
											isChecked={filter.id === selectedOption?.id}
											confirmFilterSelection={confirmFilterSelection}
										/>
									);
								})}
							</FiltersListWrapper>
						</SimpleScrollView>
					</Dialog.ScrollArea>
					<Dialog.Actions style={{ justifyContent: 'space-between', paddingHorizontal: 15 }}>
						<Button onPress={onDialogCancel}>
							<FormattedMessage defaultMessage="Zrušit" description={'confirmDialogue-cancel'} />
						</Button>
						<Button onPress={onDialogConfirm}>
							<FormattedMessage {...filterActionIntlString} />
						</Button>
					</Dialog.Actions>
				</StyledDialog>
			</Portal>
		</Wrapper>
	);
};

const StyledDialog = styled(Dialog)`
	max-height: ${SCREEN_HEIGHT * 0.7}px;
	margin: 0 ${SPACING};
`;

const FiltersListWrapper = styled.View`
	padding: 10px 0;
`;

const Wrapper = styled(JustifiedContent)`
	align-items: center;
	width: 100%;
`;
