import { FC } from 'react';
import { LIGHT_GREY } from '../../constants/common';
import { MaterialIcons } from '@expo/vector-icons';
import { GREEN } from '../../constants/common';
import styled from 'styled-components/native';

type Props = {
	isChecked: boolean;
};

export const FilterCheckbox: FC<Props> = ({ isChecked }) => {
	return (
		<Checkbox style={{ backgroundColor: isChecked ? 'transparent' : LIGHT_GREY }}>
			{isChecked && <MaterialIcons name="check" size={24} color={GREEN} />}
		</Checkbox>
	);
};

const Checkbox = styled.View`
	height: 30px;
	width: 30px;
	border-radius: 25px;
	margin-right: 10px;
	border: 1px solid ${LIGHT_GREY};
	align-items: center;
	justify-content: center;
`;
