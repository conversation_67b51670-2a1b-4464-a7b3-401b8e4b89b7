import React, { FC, useContext } from 'react';
import { Filter } from '../../types/filter';
import { generateFilterData } from '../../utils/generateFilterData';
import { SmallerText } from '../../components/UI/SmallerText';
import { View } from 'react-native';
import { useIntl } from 'react-intl';
import { LocalesContext } from '../../contexts/LocalesContext';
import { BoldParagraph } from '../UI/Typography';

type Props = {
	filter: Filter;
};

export const FilterOptionContent: FC<Props> = ({ filter }) => {
	const { currentLanguage } = useContext(LocalesContext);
	const intl = useIntl();
	const { areaData, dispositionData, landAreaData, landTypeData, locationData, mainLabel, priceData } = generateFilterData(
		filter,
		intl,
		currentLanguage,
	);

	return (
		<View>
			<BoldParagraph>{mainLabel?.value}</BoldParagraph>
			<SmallerText>{locationData?.value}</SmallerText>
			<SmallerText>
				{priceData.label} {priceData?.value}
			</SmallerText>
			{dispositionData?.value && (
				<SmallerText>
					{dispositionData.label} {dispositionData.value}
				</SmallerText>
			)}
			{landTypeData?.value && (
				<SmallerText>
					{landTypeData.label} {landTypeData.value}
				</SmallerText>
			)}
			{areaData?.value && (
				<SmallerText>
					{areaData.label} {areaData.value}
				</SmallerText>
			)}
			{landAreaData?.value && (
				<SmallerText>
					{landAreaData.label} {landAreaData.value}
				</SmallerText>
			)}
		</View>
	);
};
