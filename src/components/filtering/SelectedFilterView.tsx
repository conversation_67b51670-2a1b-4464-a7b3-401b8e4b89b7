import React, { FC, useContext } from 'react';
import { SPACING } from '../../constants/common';
import { Filter } from '../../types/filter';
import { generateFilterData } from '../../utils/generateFilterData';
import { View } from 'react-native';
import { Subheading } from 'react-native-paper';
import { JustifiedContent } from '../../components/UI/JustifiedContent';
import { SmallerText } from '../../components/UI/SmallerText';
import styled from 'styled-components/native';
import { BaseChip, FilterChip } from './FilterChips';
import { useIntl } from 'react-intl';
import { LocalesContext } from '../../contexts/LocalesContext';

type Props = {
	activeFilter: Filter;
	showDialog: () => void;
	resetFilters: () => void;
};

export const SelectedFilterView: FC<Props> = ({ activeFilter, showDialog, resetFilters }) => {
	const intl = useIntl();
	const { currentLanguage } = useContext(LocalesContext);
	const { mainLabel, areaData, dispositionData, landAreaData, landTypeData, locationData, priceData } = generateFilterData(
		activeFilter,
		intl,
		currentLanguage,
	);
	const detailsToShow = [priceData, dispositionData, landTypeData, areaData, landAreaData]
		.filter((item) => !!item.value)
		.map((item) => `${item.label} ${item.value}`);

	return (
		<PaddedView>
			<Subheading>{mainLabel.value}</Subheading>
			<SmallerText>
				{locationData.value}. {detailsToShow.join('. ')}
			</SmallerText>
			<ButtonsWrapper>
				<FilterChip showDialog={showDialog} label="Změnit filtr" />
				<ButtonWrapper>
					<BaseChip icon="close-circle" onPress={resetFilters} label="Zrušit" />
				</ButtonWrapper>
			</ButtonsWrapper>
		</PaddedView>
	);
};

const ButtonsWrapper = styled(JustifiedContent)`
	margin-top: ${SPACING};
	align-self: flex-start;
`;

const ButtonWrapper = styled.View`
	padding-left: ${SPACING};
`;

const PaddedView = styled.View`
	padding-bottom: ${SPACING};
`;
