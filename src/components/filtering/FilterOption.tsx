import { FC } from 'react';
import { Subheading } from 'react-native-paper';
import styled from 'styled-components/native';
import { FilterCheckbox } from './FilterCheckbox';
import { Filter } from '../../types/filter';
import { FilterOptionContent } from './FilterOptionContent';
import { FormattedMessage } from 'react-intl';

type Props = {
	filter: Filter | null;
	isChecked: boolean;
	confirmFilterSelection: (filter: Filter | null) => void;
};

export const FilterOption: FC<Props> = ({ filter, isChecked, confirmFilterSelection }) => {
	return (
		<StyledTouchableOpacity onPress={() => confirmFilterSelection(filter)}>
			<FilterCheckbox isChecked={isChecked} />
			{filter ? (
				<FilterOptionContent filter={filter} />
			) : (
				<Subheading style={{ fontWeight: 'bold' }}>
					<FormattedMessage defaultMessage="Všechny aktivní filtry" description="mobile: allActiveFilters" />
				</Subheading>
			)}
		</StyledTouchableOpacity>
	);
};

const StyledTouchableOpacity = styled.TouchableOpacity`
	flex-direction: row;
	margin: 10px 0;
`;
