import { FC } from 'react';
import { Button } from 'react-native-paper';
import styled from 'styled-components/native';
import { SPACING } from '../constants/common';

type Props = {
	text: string;
	loading?: boolean;
	onPress?: () => void;
};

export const MainButton: FC<Props> = ({ text, loading = false, onPress }) => {
	return (
		<StyledButton mode="contained" labelStyle={{ color: 'white' }} onPress={onPress} loading={loading}>
			{text}
		</StyledButton>
	);
};

export const StyledButton = styled(Button)`
	width: 300px;
	margin: ${SPACING} 0;
	padding: 4px 0;
	font-weight: bold;
`;
