import * as SplashScreen from 'expo-splash-screen';
import { ReactElement, useContext, useEffect } from 'react';
import { AuthContext } from '../contexts/AuthContext';

type Props = {
	children: ReactElement<any, any> | null;
};

export const TokenLoadBuffer = ({ children }: Props) => {
	const { isCheckingForToken } = useContext(AuthContext);

	useEffect(() => {
		if (isCheckingForToken) {
			SplashScreen.preventAutoHideAsync();
		} else {
			SplashScreen.hideAsync();
		}
	}, [isCheckingForToken]);

	if (isCheckingForToken) {
		return null; // Keep splash screen visible
	} else {
		return children;
	}
};
