import AppLoading from 'expo-app-loading';
import { ReactElement, useContext } from 'react';
import { AuthContext } from '../contexts/AuthContext';

type Props = {
	children: ReactElement<any, any> | null;
};

export const TokenLoadBuffer = ({ children }: Props) => {
	const { isCheckingForToken } = useContext(AuthContext);
	if (isCheckingForToken) {
		return <AppLoading />;
	} else {
		return children;
	}
};
