import React, { FC } from 'react';
import { Platform, Keyboard, TouchableWithoutFeedback } from 'react-native';
import styled from 'styled-components/native';

export const KeyboardAvoider: FC = ({ children }) => {
	return (
		<StyledKeyboardAvoider behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
			<TouchableWithoutFeedback onPress={Keyboard.dismiss}>{children}</TouchableWithoutFeedback>
		</StyledKeyboardAvoider>
	);
};

const StyledKeyboardAvoider = styled.KeyboardAvoidingView`
	flex: 1;
`;
