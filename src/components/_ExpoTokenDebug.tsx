import React, { useEffect, useState } from 'react';
import { Text, TextInput } from 'react-native';
import { getPushNotificationToken } from '../utils/notifications/tokenStorage';

type Props = {};

export function ExpoTokenDebug(props: Props) {
	const [token, setToken] = useState<string>();

	useEffect(() => {
		getPushNotificationToken().then(setToken);
	});

	return <TextInput value={token} />;
}

ExpoTokenDebug.displayName = 'ExpoTokenDebug';
