import { DrawerContentScrollView } from '@react-navigation/drawer';
import { Drawer } from 'react-native-paper';
import styled from 'styled-components/native';
import { LIGHT_GREY } from '../../constants/common';
import { Logo } from '../Logo';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useLogout } from '../../utils/useLogout';
import { APP_ROUTES, createLocalisedRouteLabel } from '../../constants/routes';
import { LanguagePicker } from '../LanguagePicker';
import { useIntl } from 'react-intl';

export const CustomNavigationDrawer = ({ ...navProps }) => {
	const { state, navigation } = navProps;
	const { logout } = useLogout({ navigation });
	const intl = useIntl();

	const isSelected = (index: number) => index === state.index;

	return (
		<DrawerContentScrollView {...navProps}>
			<LogoWrapper>
				<Logo variant="small" />
				<MaterialCommunityIcons name="window-close" size={24} color="black" onPress={navigation.closeDrawer} />
			</LogoWrapper>
			{state.routes.map((route: any, index: number) => (
				<StyledDrawerItem
					key={route.key}
					label={createLocalisedRouteLabel(route.name, intl)}
					active={isSelected(index)}
					onPress={() => {
						const realRoute = Object.values(APP_ROUTES).find((appRoute) => appRoute.title === route.name);
						realRoute && navigation.navigate(realRoute.name);
					}}
				/>
			))}

			<StyledDrawerItem
				label={intl.formatMessage({ description: 'userSectionMenu-logOut', defaultMessage: 'Odhl\u00e1\u0161en\u00ed' })}
				onPress={logout}
			/>
			<LanguagePicker closeDrawer={navigation.closeDrawer} />
		</DrawerContentScrollView>
	);
};


const StyledDrawerItem = styled(Drawer.Item)`
	border-bottom-width: 1px;
	border-color: ${LIGHT_GREY};
	padding: 10px 12px;
	border-radius: 0;
	margin: 0;
`;

const LogoWrapper = styled.View`
	border-bottom-width: 1px;
	border-color: ${LIGHT_GREY};
	padding: 26px 20px 30px;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
`;
