import { NativeStackNavigationOptions } from '@react-navigation/native-stack';
import { FC } from 'react';
import { useIntl } from 'react-intl';
import { Appbar } from 'react-native-paper';
import styled from 'styled-components/native';
import { LIGHT_GREY, WHITE } from '../../constants/common';
import { APP_ROUTES, createLocalisedRouteLabel } from '../../constants/routes';
import { NavigationProps } from '../../types/common';
import { Logo } from '../Logo';

const { dashboardRoute } = APP_ROUTES;

type Props = NavigationProps & {
	options: NativeStackNavigationOptions;
	back?: NativeStackNavigationOptions;
};

export const CustomNavigationBar: FC<Props> = ({ navigation, options, back }) => {
	const isHomeScreen = options.title === dashboardRoute.title;
	const intl = useIntl();

	return (
		<StyledAppHeader>
			{back ? <Appbar.BackAction onPress={navigation.goBack} /> : null}
			{isHomeScreen ? (
				<Appbar.Content title={<Logo variant="xsmall" />} />
			) : (
				<Appbar.Content title={createLocalisedRouteLabel(options.title || '', intl)} titleStyle={{ fontWeight: 'bold' }} />
			)}

			<Appbar.Action icon="menu" onPress={navigation.toggleDrawer} />
		</StyledAppHeader>
	);
};

const StyledAppHeader = styled(Appbar.Header)`
	background-color: ${WHITE};
	border-bottom-width: 1px;
	border-color: ${LIGHT_GREY};
`;
