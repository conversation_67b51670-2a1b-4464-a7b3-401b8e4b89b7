import { FC } from 'react';
import LogoSVG from '../../assets/logo-visidoo-color.svg';
import styled from 'styled-components/native';

type Variant = 'xsmall' | 'small' | 'large';

type Props = {
	variant: Variant;
};

type Measurement = 'width' | 'height';
type Size = Record<Variant, number>;

const sizes: Record<Measurement, Size> = {
	width: {
		xsmall: 80,
		small: 120,
		large: 180,
	},
	height: {
		xsmall: 30,
		small: 40,
		large: 60,
	},
};

export const Logo: FC<Props> = ({ variant }) => {
	const width = sizes.width[variant];
	const height = sizes.height[variant];

	// const LogoWrapper = styled.Text`
	// 	width: ${width}px;
	// 	height: ${height}px;
	// 	display: flex;
	// 	background: red;
	// `;

	// return (
	// 	<LogoWrapper>
	// 		<LogoSVG width="100%" height="100%" />
	// 	</LogoWrapper>
	// );

	return <LogoSVG width={width} height={height} />;
};
