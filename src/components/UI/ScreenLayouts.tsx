import React, { FC } from 'react';
import styled from 'styled-components/native';
import { SCREEN_WIDTH, SPACING } from '../../constants/common';
import { PushNotificationsController } from '../../utils/notifications/PushNotificationsController';

type Props = {
	variant?: 'centered' | 'flexStart';
};

export const ScreenLayout: FC<Props> = ({ variant = 'centered', children }) => {
	const Wrapper = variant === 'centered' ? CenteredWrapper : FlexStartWrapper;

	return (
		<Wrapper>
			<PushNotificationsController />
			{children}
		</Wrapper>
	);
};

const BaseWrapper = styled.View`
	align-items: center;
	background-color: white;
	flex: 1;
	width: ${SCREEN_WIDTH}px;
`;

const CenteredWrapper = styled(BaseWrapper)`
	justify-content: center;
`;

const FlexStartWrapper = styled(BaseWrapper)`
	justify-content: flex-start;
	padding: ${SPACING};
`;
