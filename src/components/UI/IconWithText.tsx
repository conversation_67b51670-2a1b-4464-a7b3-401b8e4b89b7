import { FC } from 'react';
import styled from 'styled-components/native';
import { JustifiedContent } from './JustifiedContent';

type Props = {
	iconComponent: any;
};

export const IconWithText: FC<Props> = ({ iconComponent, children }) => {
	return (
		<Wrapper>
			<IconWrapper>{iconComponent}</IconWrapper>
			{children}
		</Wrapper>
	);
};

const Wrapper = styled(JustifiedContent)`
	align-items: center;
`;

const IconWrapper = styled.View`
	margin-right: 4px;
`;
