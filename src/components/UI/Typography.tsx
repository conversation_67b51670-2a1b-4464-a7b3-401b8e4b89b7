import { MEDIUM_FONT_SIZE, MEDIUM_GREY, PRIMARY_COLOR } from '../../constants/common';
import styled from 'styled-components/native';
import { Paragraph } from 'react-native-paper';

export const BoldTitle = styled.Text`
	color: ${PRIMARY_COLOR};
	font-weight: bold;
	font-size: ${MEDIUM_FONT_SIZE};
	line-height: 20px;
`;

export const LightParagraph = styled(Paragraph)`
	color: ${MEDIUM_GREY};
`;

export const BoldParagraph = styled(Paragraph)`
	font-weight: bold;
`;
