import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

const strategy =
	Platform.OS === 'web'
		? {
				saveToStorage: (key: string, value: string) => {
					const r = localStorage.setItem(key, value);
					return Promise.resolve(r);
				},
				fetchFromStorage: (key: string) => {
					const r = localStorage.getItem(key);
					return Promise.resolve(r);
				},
				deleteFromStorage: (key: string) => {
					const r = localStorage.removeItem(key);
					return Promise.resolve(r);
				},
		  }
		: {
				saveToStorage: (key: string, value: string) => {
					return SecureStore.setItemAsync(key, value);
				},
				fetchFromStorage: (key: string) => {
					return SecureStore.getItemAsync(key);
				},
				deleteFromStorage: (key: string) => {
					return SecureStore.deleteItemAsync(key);
				},
		  };

export const saveToStorage = strategy.saveToStorage;

export const fetchFromStorage = strategy.fetchFromStorage;

export const deleteFromStorage = strategy.deleteFromStorage;
