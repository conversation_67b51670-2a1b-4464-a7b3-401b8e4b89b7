import { RealEstateType } from '../types/filter';
import { Offer } from '../types/offer';
import { formatNumber } from './formats';
import { getLocaleLabel } from './getLocaleLabel';

export const createOfferTitle = (offer: Offer) => {
	const { adType, realEstateType, disposition, area, landArea, landType } = offer;
	const parts = [
		getLocaleLabel({ realEstateType, adType, landType }),
		realEstateType === RealEstateType.APARTMENT ? disposition : '',
		realEstateType === RealEstateType.LAND && landArea ? `${formatNumber(landArea)} m²` : '',
		realEstateType !== RealEstateType.LAND && area ? `${formatNumber(area)} m²` : '',
		realEstateType === RealEstateType.HOUSE && landArea ? `s pozemkem ${formatNumber(landArea)} m²` : '',
	];
	return parts.filter((part) => part).join(' ');
};
