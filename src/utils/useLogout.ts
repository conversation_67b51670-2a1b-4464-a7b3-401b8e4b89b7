import { useContext, useMemo } from 'react';
import { API_BASE_URL, LOGOUT_ENDPOINT } from '../constants/common';
import { APP_ROUTES } from '../constants/routes';
import { AuthContext } from '../contexts/AuthContext';
import { NavigationProps } from '../types/common';
import { getPushNotificationToken, removePushNotificationToken } from './notifications/tokenStorage';

const { dashboardRoute } = APP_ROUTES;

type Props = {
	navigation: NavigationProps['navigation'];
};

export function useLogout(props: Props) {
	const { navigation } = props;
	const { token, deleteToken } = useContext(AuthContext);

	return useMemo(
		() => ({
			logout: async () => {
				navigation.closeDrawer();
				try {
					const notificationToken = await getPushNotificationToken();
					const url = `${API_BASE_URL}${LOGOUT_ENDPOINT}`;
					await Promise.all([
						fetch(url, {
							method: 'POST',
							headers: {
								Authorization: `Bearer ${token}`,
								'Content-Type': 'application/json',
							},
							body: JSON.stringify({ notificationToken }),
						}),
						removePushNotificationToken(),
					]);
				} catch (error) {
					console.error(error);
				}
				await deleteToken();
				navigation.navigate(dashboardRoute.title);
			},
		}),
		[navigation, token, deleteToken],
	);
}
