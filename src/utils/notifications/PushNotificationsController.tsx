import { useCallback, useEffect, useState } from 'react';
import * as Notifications from 'expo-notifications';
import * as TaskManager from 'expo-task-manager';
import { getPushNotificationToken, subscribe } from './tokenStorage';
import { NotificationData } from '../../types/notification';
import { useNavigation } from '@react-navigation/native';
import { APP_ROUTES } from '../../constants/routes';
import { createOfferTitle } from '../createOfferTitle';

// TODO: intl notifications text!

const BACKGROUND_NOTIFICATION_TASK = 'BACKGROUND-NOTIFICATION-TASK';

TaskManager.defineTask(BACKGROUND_NOTIFICATION_TASK, async ({ data, error, executionInfo }) => {
	console.debug('Task executor:', data, error, executionInfo);
	try {
		let count = 0;
		count = await Notifications.getBadgeCountAsync();
		await Notifications.setBadgeCountAsync(count + 1);
	} catch (err) {
		console.debug(err);
	}
});

type Props = {};

export function PushNotificationsController(props: Props) {
	const [token, setToken] = useState<string>();
	const navigation = useNavigation();
	const lastNotificationResponse = Notifications.useLastNotificationResponse();

	const handleNotificationResponse = useCallback(
		(data: NotificationData) => {
			if (!('type' in data)) {
				return;
			}

			if (data.type === 'NEW_OFFER') {
				const { offer } = data;
				const date = new Date(offer.createdAt);
				const month = date.getMonth() + 1;
				const day = date.getDate();
				const hours = date.getHours();
				const mins = date.getMinutes();
				const offerTime = `${month}.${day} ${hours}:${mins}`;
				// @ts-ignore
				navigation.navigate(APP_ROUTES.offerDetailsRoute.name, { offer, title: createOfferTitle(offer), offerTime });
			}
		},
		[navigation],
	);

	useEffect(() => {
		if (lastNotificationResponse?.notification.request.content.data) {
			handleNotificationResponse(lastNotificationResponse.notification.request.content.data as NotificationData);
		}
	}, [lastNotificationResponse, handleNotificationResponse]);

	useEffect(() => {
		const unsubscribe = subscribe(setToken);
		getPushNotificationToken().then(setToken);

		return unsubscribe;
	}, []);

	useEffect(() => {
		if (!token) {
			return;
		}

		Notifications.setNotificationHandler({
			handleNotification: async () => ({
				shouldShowAlert: true,
				shouldPlaySound: true,
				shouldSetBadge: true,
			}),
		});

		// register task to run whenever is received while the app is in the background
		Notifications.registerTaskAsync(BACKGROUND_NOTIFICATION_TASK);

		// listener triggered whenever a notification is received while the app is in the foreground
		const foregroundReceivedNotificationSubscription = Notifications.addNotificationReceivedListener(async (notification) => {
			console.debug('NEW NOTIFICATION', notification.request?.content?.data?.type); // TODO: remove
			try {
				const badge = notification.request?.content?.data?.badge;
				if (badge && typeof badge === 'number') {
					await Notifications.setBadgeCountAsync(badge);
				}
			} catch (_) {}
		});

		return () => {
			foregroundReceivedNotificationSubscription.remove();
			TaskManager.isTaskRegisteredAsync(BACKGROUND_NOTIFICATION_TASK).then((isRegistered) => {
				if (isRegistered) {
					Notifications.unregisterTaskAsync(BACKGROUND_NOTIFICATION_TASK);
				}
			});
		};
	}, [token, navigation]);

	return null;
}

PushNotificationsController.displayName = 'PushNotificationsController';
