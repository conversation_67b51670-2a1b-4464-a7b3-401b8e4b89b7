import * as Notifications from 'expo-notifications';
import { NotificationChannelInput } from 'expo-notifications';
import { Platform } from 'react-native';
import { ERROR_PUSHNOTI_NOT_GRANTED } from '../../constants/common';
import { CommonError } from '../CommonError';

export type ChannelParams = [id: string, input: NotificationChannelInput];

const defaultParams: ChannelParams = [
	'default',
	{
		name: 'default',
		importance: Notifications.AndroidImportance.MAX,
		vibrationPattern: [0, 250, 250, 250],
		lightColor: '#FF231F7C',
	},
];

export async function registerForPushNotifications(channelInput: ChannelParams = defaultParams) {
	const { status: existingStatus } = await Notifications.getPermissionsAsync();
	let finalStatus = existingStatus;
	if (existingStatus !== 'granted') {
		const { status } = await Notifications.requestPermissionsAsync({
			ios: {
				allowAlert: true,
				allowBadge: true,
				allowSound: true,
			},
		});
		finalStatus = status;
	}
	if (finalStatus !== 'granted') {
		throw new CommonError(`Failed to get permissions for push notifications (status: ${finalStatus})!`, ERROR_PUSHNOTI_NOT_GRANTED);
	}

	const token = (await Notifications.getExpoPushTokenAsync()).data;

	if (Platform.OS === 'android') {
		Notifications.setNotificationChannelAsync(...channelInput);
	}

	return token;
}
