import * as SecureStore from 'expo-secure-store';

type Subscriber = (token?: string) => void;

let subscribers: Array<Subscriber> = [];

const PN_TOKEN = 'pushNotificationToken';

export const getPushNotificationToken = async () => {
	const token = (await SecureStore.getItemAsync(PN_TOKEN)) || undefined;
	return token;
};

export const savePushNotificationToken = async (token: string) => {
	await SecureStore.setItemAsync(PN_TOKEN, token);
	subscribers.forEach((s) => s(token));
};

export const removePushNotificationToken = async () => {
	await SecureStore.deleteItemAsync(PN_TOKEN);
	subscribers.forEach((s) => s());
};

export const subscribe = (subscriber: Subscriber) => {
	subscribers.push(subscriber);
	return function unsubscribe() {
		subscribers = subscribers.filter((current) => subscriber !== current);
	};
};
