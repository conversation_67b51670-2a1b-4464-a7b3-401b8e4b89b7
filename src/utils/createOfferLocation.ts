import { Location } from '../types/filter';

type Props = {
	location: Location;
	display?: ('part' | 'city' | 'district' | 'region' | 'cohesion' | 'country')[];
};

export const createOfferLocation = ({ location, display = ['city', 'district'] }: Props) => {
	const { name, part, city, district, region, cohesion, country } = location;

	const parts = [
		name,
		display.includes('part') && part && part !== name ? part : '',
		display.includes('city') && city && city !== name ? city : '',
		display.includes('district') && district && district !== name ? district : '',
		display.includes('region') && region && region !== name ? region : '',
		display.includes('cohesion') && cohesion && cohesion !== name ? cohesion : '',
		display.includes('country') && country && country !== name ? country : '',
	];

	return parts.filter((part) => part).join(', ');
};
