import { SupportedLocale } from '../i18n/supportedLocales';

let locale = 'cs-CZ';

export const setLocale = (newLocale: string) => (locale = newLocale);

export const resetLocale = () => (locale = 'cs-CZ');

export const formatNumber = (value: number, minimumFractionDigits = 0, maximumFractionDigits = minimumFractionDigits) =>
	value.toLocaleString(locale, { minimumFractionDigits, maximumFractionDigits });

export const formatPercent = (value: number, minimumFractionDigits = 0, maximumFractionDigits = minimumFractionDigits) =>
	value.toLocaleString(locale, { style: 'percent', minimumFractionDigits, maximumFractionDigits });

export const formatDate = (date: Date) => date.toLocaleDateString(locale);

export const formatDateTime = (date: Date) => date.toLocaleString(locale);

export const formatTime = (date: Date) => date.toLocaleTimeString(locale);

export const normalizeString = (string: string, keepSpaces = false) =>
	string
		.toLowerCase()
		.replace(/\s/g, keepSpaces ? ' ' : '-')
		.normalize('NFD')
		.replace(/[\u0300-\u036f]/g, '');

export const uppercaseFirst = (string: string, locale: SupportedLocale) => string[0].toLocaleUpperCase(locale) + string.substring(1);
