import { IntlShape } from 'react-intl';
import { commonInltMessages } from '../i18n/commonMessages';
import { useFormattedPriceString } from '../i18n/prices';
import { Locale } from '../i18n/supportedLocales';
import { Filter, FilterOption, RealEstateType } from '../types/filter';
import { uppercaseFirst, formatNumber } from './formats';
import { getFiltersLocalizedStrings } from './getFiltersLocalizedStrings';

const { notSpecifiedLabel } = commonInltMessages;

export const generateFilterData = (filter: Filter, intl: IntlShape, currentLanguage: Locale): Record<string, FilterOption> => {
	const { generateLabel } = getFiltersLocalizedStrings(intl, currentLanguage);
	const { realEstateType, location, disposition, landType, price, area, landArea, radius, adType } = filter;
	const NOT_SPECIFIED = intl.formatMessage(notSpecifiedLabel);

	const formattedPriceGte = useFormattedPriceString(price.gte);
	const formattedPriceLte = useFormattedPriceString(price.lte);

	const mainLabel = {
		label: 'mainLabel',
		value: intl.formatMessage(
			{ defaultMessage: 'Chci {adType} {realEstateType}', description: 'filterBox.iWant' },
			{ adType: generateLabel({ adType }), realEstateType: generateLabel({ realEstateType }).toLowerCase() },
		),
	};

	const locationData = {
		label: 'location',
		value: `${location.city}${location.district ? `, ${location.district}` : ''}${
			typeof radius !== 'undefined' && radius > 0 ? ` + ${radius} km` : ''
		}`,
	};

	const priceData = {
		label: intl.formatMessage({ defaultMessage: 'Cena: ', description: 'filterBox-price' }),
		value:
			price.gte === 0 && !price.lte
				? NOT_SPECIFIED
				: `${
						price.gte > 0
							? `${intl.formatMessage(
									{ defaultMessage: 'od {price}', description: 'filterBox-priceFrom' },
									{ price: formattedPriceGte },
							  )}`
							: ''
				  } ${
						price.lte
							? `${intl.formatMessage(
									{ defaultMessage: 'do {price}', description: 'filterBox-priceTo' },
									{ price: formattedPriceLte },
							  )}`
							: ''
				  }`,
	};

	const dispositionData = {
		label: intl.formatMessage({ defaultMessage: 'Dispozice: ', description: 'filterBox-flatLayout' }),
		value: realEstateType !== RealEstateType.APARTMENT ? null : disposition.length > 0 ? disposition.join(', ') : NOT_SPECIFIED,
	};

	const landTypeData = {
		label: intl.formatMessage({ defaultMessage: 'Druh pozemku: ', description: 'filterBox-landType' }),
		value:
			realEstateType !== RealEstateType.LAND
				? null
				: landType.length > 0
				? landType.map((type) => uppercaseFirst(generateLabel({ landType: type }), currentLanguage)).join(', ')
				: NOT_SPECIFIED,
	};

	const areaData = {
		label: intl.formatMessage({ defaultMessage: 'Plocha: ', description: 'filterBox-area' }),
		value:
			realEstateType !== RealEstateType.APARTMENT && realEstateType !== RealEstateType.HOUSE
				? null
				: area.gte === 0 && !area.lte
				? NOT_SPECIFIED
				: `${area.gte > 0 ? fromMetersFormattedMessage(area.gte, intl) : ''} ${
						area.lte ? toMetersFormattedMessage(area.lte, intl) : ''
				  }`,
	};

	const landAreaData = {
		label: intl.formatMessage({ defaultMessage: 'Plocha pozemku: ', description: 'filterBox-landArea' }),
		value:
			realEstateType !== RealEstateType.HOUSE && realEstateType !== RealEstateType.LAND
				? null
				: landArea.gte === 0 && !landArea.lte
				? NOT_SPECIFIED
				: `${landArea.gte > 0 ? fromMetersFormattedMessage(landArea.gte, intl) : ''} ${
						landArea.lte ? toMetersFormattedMessage(landArea.lte, intl) : ''
				  }`,
	};

	return { mainLabel, locationData, priceData, dispositionData, landTypeData, areaData, landAreaData };
};

const fromMetersFormattedMessage = (areaValue: number, intl: IntlShape) => {
	return intl.formatMessage({ description: 'filterBox-metersFrom', defaultMessage: 'od {area} m² ' }, { area: formatNumber(areaValue) });
};

const toMetersFormattedMessage = (areaValue: number, intl: IntlShape) => {
	return intl.formatMessage({ description: 'filterBox-metersTo', defaultMessage: 'do {area} m² ' }, { area: formatNumber(areaValue) });
};
