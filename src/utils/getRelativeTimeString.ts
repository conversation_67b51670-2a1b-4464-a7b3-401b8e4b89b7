export const getRelativeTimeString = (date: number, style?: Intl.RelativeTimeFormatStyle) => {
	const formatter = new Intl.RelativeTimeFormat('cs', { style, numeric: 'auto' });
	const now = new Date().getTime();
	const dateTime = new Date(date).getTime();
	const diff = dateTime - now;

	const seconds = Math.ceil(diff / 1000);
	const minutes = Math.floor(seconds / 60);
	const hours = Math.ceil(minutes / 60);
	const days = Math.ceil(hours / 24);
	const weeks = Math.ceil(days / 7);
	const months = Math.ceil(weeks / 4);
	const years = Math.ceil(months / 12);

	if (Math.abs(years) > 1) {
		return formatter.format(years, 'years');
	} else if (Math.abs(years) === 1) {
		return formatter.format(years, 'year');
	} else if (Math.abs(months) > 1) {
		return formatter.format(months, 'months');
	} else if (Math.abs(months) === 1) {
		return formatter.format(months, 'month');
	} else if (Math.abs(weeks) > 1) {
		return formatter.format(weeks, 'weeks');
	} else if (Math.abs(weeks) === 1) {
		return formatter.format(weeks, 'week');
	} else if (Math.abs(days) > 1) {
		return formatter.format(days, 'days');
	} else if (Math.abs(days) === 1) {
		return formatter.format(days, 'day');
	} else if (Math.abs(hours) > 1) {
		return formatter.format(hours, 'hours');
	} else if (Math.abs(hours) === 1) {
		return formatter.format(hours, 'hour');
	} else if (Math.abs(minutes) > 1) {
		return formatter.format(minutes, 'minutes');
	} else {
		return formatter.format(minutes, 'minute');
	}
};
