import { IntlShape } from 'react-intl';
import { commonInltMessages } from '../i18n/commonMessages';
import { RealEstateType } from '../types/filter';
import { Offer, OfferType } from '../types/offer';
import { formatNumber } from './formats';

const NOTHING = '';

export const createOfferPrice = (offer: Offer, intl: IntlShape) => {
	const { price, priceNote, priceText, adType } = offer;
	const noPriceIntlString = intl.formatMessage({ description: 'offerPrice-noPriceInfo', defaultMessage: 'Žádné informace o ceně' });
	const priceFormatted = price ? formatNumber(price) : priceText || priceNote || noPriceIntlString;
	const { monthlyIntlString } = commonInltMessages;
	return `${priceFormatted}${price ? ' Kč' : ''}${adType === OfferType.RENT ? ` ${intl.formatMessage(monthlyIntlString)}` : ''}`;
};

export const createOfferPricePerSqm = (offer: Offer) => {
	const { price, realEstateType, landArea, area } = offer;
	if (!price || (realEstateType === RealEstateType.LAND && !landArea) || (realEstateType !== RealEstateType.LAND && !area))
		return NOTHING;
	const selectedArea = realEstateType === RealEstateType.LAND ? landArea : area;
	const roundedPrice = Math.round(price / selectedArea);

	return `${formatNumber(roundedPrice)} Kč/m²`;
};
