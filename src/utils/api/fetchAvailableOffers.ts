import { API_BASE_URL, OFFERS_ENDPOINT } from '../../constants/common';
import { Maybe } from '../../types/common';
import { OfferResponse } from '../../types/offer';

export const fetchAvailableOffers = async (token: string, page: number, filterId: Maybe<string>) => {
	const url = `${API_BASE_URL}${OFFERS_ENDPOINT}?page=${page}${filterId ? ' &filterId=' + filterId : ''}`;
	const res = await fetch(url, {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
	});

	if (res.status !== 200) {
		throw new Error(String(res.status));
	}
	const parsedResponse: { data: OfferResponse } = await res.json();
	return parsedResponse.data;
};
