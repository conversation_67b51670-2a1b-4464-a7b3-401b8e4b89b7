import { API_BASE_URL, FILTERS_ENDPOINT } from '../../constants/common';
import { FiltersResponse } from '../../types/filter';

export const fetchUserFilters = async (token: string): Promise<FiltersResponse> => {
	const apiUrl = `${API_BASE_URL}${FILTERS_ENDPOINT}`;
	const res = await fetch(apiUrl, {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${token}`,
		},
	});

	if (res.status !== 200) {
		throw new Error(String(res.status));
	}

	const parsedData: { data: FiltersResponse } = await res.json();

	return parsedData.data;
};
