import { LandType, RealEstateType } from '../types/filter';
import { OfferType } from '../types/offer';
import { uppercaseFirst } from './formats';

// ... nemovitosti
const adLabelsNoun = {
	[OfferType.AUCTION]: 'dražba',
	[OfferType.OTHER]: 'nabídka',
	[OfferType.RENT]: 'pronájem',
	[OfferType.SALE]: 'prodej',
	[OfferType.ROOMMATES]: 'spolubdlení',
};

// Chci ...
const adLabelsVerb = {
	[OfferType.AUCTION]: 'dražit',
	[OfferType.OTHER]: 'nabídnout',
	[OfferType.RENT]: 'pronajmout',
	[OfferType.SALE]: 'koupit',
	[OfferType.ROOMMATES]: 'spolubydlet',
};

// Filtr pro ...
const realEstateLabelsFirstFall = {
	[RealEstateType.APARTMENT]: 'byt',
	[RealEstateType.HOUSE]: 'dům',
	[RealEstateType.LAND]: 'pozemek',
	[RealEstateType.COMMERCIAL]: 'komerční objekt',
	[RealEstateType.OTHER]: 'nemovitost',
};

// Nabídka ...
const realEstateLabelsThirdFall = {
	[RealEstateType.APARTMENT]: 'bytu',
	[RealEstateType.HOUSE]: 'domu',
	[RealEstateType.LAND]: 'pozemku',
	[RealEstateType.COMMERCIAL]: 'komerčního objektu',
	[RealEstateType.OTHER]: 'nemovitosti',
};

// ... pozemek
const landTypeLabelsFirstFall = {
	[LandType.BUILD_PLOT]: 'stavební',
	[LandType.COMMERCIAL]: 'komerční',
	[LandType.OTHER]: 'užitkový',
};

// ... pozemku
const landTypeLabelsThirdFall = {
	[LandType.BUILD_PLOT]: 'stavebního',
	[LandType.COMMERCIAL]: 'komerčního',
	[LandType.OTHER]: 'užitkového',
};

type Props = {
	adType?: OfferType;
	landType?: LandType;
	realEstateType?: RealEstateType;
};

export const getLocaleLabel = ({ realEstateType, adType, landType }: Props) => {
	if (adType && realEstateType) {
		if (landType && realEstateType === RealEstateType.LAND) {
			return uppercaseFirst(
				`${adLabelsNoun[adType]} ${landTypeLabelsThirdFall[landType]} ${realEstateLabelsThirdFall[realEstateType]}`,
			);
		}

		return uppercaseFirst(`${adLabelsNoun[adType]} ${realEstateLabelsThirdFall[realEstateType]}`);
	}

	if (realEstateType) {
		if (landType && realEstateType === RealEstateType.LAND) {
			return uppercaseFirst(`${landTypeLabelsFirstFall[landType]} ${realEstateLabelsFirstFall[realEstateType]}`);
		}
		return uppercaseFirst(realEstateLabelsFirstFall[realEstateType]);
	}

	if (adType) {
		return adLabelsVerb[adType];
	}

	if (landType) {
		return landTypeLabelsFirstFall[landType];
	}

	return '';
};
