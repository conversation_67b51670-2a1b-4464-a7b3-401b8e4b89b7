import { useContext } from 'react';
import { useIntl } from 'react-intl';
import { LocalesContext } from '../contexts/LocalesContext';
import { Offer } from '../types/offer';
import { uppercaseFirst } from '../utils/formats';
import { getFiltersLocalizedStrings } from '../utils/getFiltersLocalizedStrings';

export const useOfferDetails = (offer: Offer) => {
	const { adType, realEstateType, area, landArea, landType, disposition, createdAt } = offer;
	const { currentLanguage } = useContext(LocalesContext);
	const intl = useIntl();
	const { generateTitle } = getFiltersLocalizedStrings(intl, currentLanguage);
	const title = uppercaseFirst(generateTitle({ adType, realEstateType, area, landArea, landType, disposition }), currentLanguage);
	const date = intl.formatDate(createdAt, { month: 'numeric', day: '2-digit' });
	const hour = intl.formatTime(createdAt);

	return { title, date, hour };
};
