import { useState, useEffect } from 'react';
import { Keyboard } from 'react-native';

export const useIsKeyboardOpen = () => {
	const [keyboardIsShowing, setKeyboardIsShowing] = useState(false);

	useEffect(() => {
		const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
			setKeyboardIsShowing(true);
		});
		const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
			setKeyboardIsShowing(false);
		});

		return () => {
			keyboardDidHideListener.remove();
			keyboardDidShowListener.remove();
		};
	}, []);

	return { keyboardIsShowing };
};
