import { useRef, useEffect } from 'react';
import { AppState, AppStateStatus } from 'react-native';

export const useOnAppActiveTaskQueue = (callbacks: Array<() => void>) => {
	const appState = useRef(AppState.currentState);

	useEffect(() => {
		AppState.addEventListener('change', handleAppStateChange);
		return () => {
			AppState.removeEventListener('change', handleAppStateChange);
		};
	}, []);

	const handleAppStateChange = (nextAppState: AppStateStatus) => {
		if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
			console.debug('App has come to the foreground! - invoking callbacks');
			callbacks.forEach((callback) => callback());
		}

		appState.current = nextAppState;
		console.debug('AppState', appState.current);
	};
};
