import { useContext } from 'react';
import { WEB_BASE_URL } from '../constants/common';
import { LocalesContext } from '../contexts/LocalesContext';
import localizedUrls from '../i18n/localizedUrls.json';

export const useLocalizedUri = (pathName: string) => {
	const { currentLanguage } = useContext(LocalesContext);
	const localizedPath = localizedUrls.find((entry) => entry.name === pathName);
	const pathToUse = localizedPath ? `${currentLanguage}${localizedPath.urls[currentLanguage]}` : pathName;
	return `${WEB_BASE_URL}/${pathToUse}`;
};
