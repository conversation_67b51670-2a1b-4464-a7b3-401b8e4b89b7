import { createContext, ReactNode, useEffect, useState } from 'react';
import { Locale, getLocaleMessages, defaultLocale } from '../i18n/supportedLocales';
import { IntlProvider } from 'react-intl';

type LocalesContextType = {
	changeLanguage: (language: Locale) => void;
	currentLanguage: Locale;
};

const initialValues: LocalesContextType = {
	changeLanguage: () => {},
	currentLanguage: defaultLocale,
};

export const LocalesContext = createContext<LocalesContextType>(initialValues);

type Props = {
	children: ReactNode;
};

export const LocalesProvider = ({ children }: Props) => {
	const [currentLanguageMessages, setCurrentLanguageMessages] = useState({});
	const [currentLanguage, setCurrentLanguage] = useState<Locale>(Locale.cs);

	const changeLanguage = (language: Locale) => {
		setCurrentLanguage(language);
		setCurrentLanguageMessages(language);
	};

	useEffect(() => {
		(async () => {
			const messages = await getLocaleMessages(currentLanguage);
			setCurrentLanguageMessages(messages);
		})();
	}, [currentLanguage]);

	return (
		<LocalesContext.Provider value={{ changeLanguage, currentLanguage }}>
			<IntlProvider locale={currentLanguage} messages={currentLanguageMessages} onError={() => null}>
				{children}
			</IntlProvider>
		</LocalesContext.Provider>
	);
};
