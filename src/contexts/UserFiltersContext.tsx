import { createContext, ReactNode, useCallback, useContext, useEffect, useState } from 'react';
import { Maybe } from '../types/common';
import { Filter } from '../types/filter';
import { fetchUserFilters } from '../utils/api/fetchUserFilters';
import { AuthContext } from './AuthContext';

type UserFiltersContextType = {
	loading: boolean;
	userFilters: Filter[];
	loadUserFilters: (token: Maybe<string>) => Promise<void>;
	refreshUserFilters: () => Promise<void>;
};

const initialValues: UserFiltersContextType = {
	loading: false,
	userFilters: [],
	loadUserFilters: (_: Maybe<string>) => Promise.resolve(),
	refreshUserFilters: () => Promise.resolve(),
};

export const UserFiltersContext = createContext<UserFiltersContextType>(initialValues);

type Props = {
	children: ReactNode;
};

export const UserFiltersProvider = ({ children }: Props) => {
	const [loading, setLoading] = useState(false);
	const [userFilters, setUserFilters] = useState<Filter[]>([]);
	const { token } = useContext(AuthContext);

	const loadUserFilters = useCallback(
		async (token: Maybe<string>) => {
			if (!token) return;
			try {
				setLoading(true);
				const filtersData = await fetchUserFilters(token);
				setUserFilters(filtersData.filters);
			} catch (error) {
				console.error(error);
			} finally {
				setLoading(false);
			}
		},
		[fetchUserFilters],
	);

	const refreshUserFilters = useCallback(async () => {
		await loadUserFilters(token);
	}, [token, loadUserFilters]);

	useEffect(() => {
		loadUserFilters(token);
	}, [token, loadUserFilters]);

	return (
		<UserFiltersContext.Provider value={{ loading, userFilters, loadUserFilters, refreshUserFilters }}>
			{children}
		</UserFiltersContext.Provider>
	);
};
