import { createContext, ReactNode, useEffect, useState } from 'react';
import { deleteFromStorage, fetchFromStorage, saveToStorage } from '../utils/storage';
const USER_TOKEN = 'user_token';

type AuthContextType = {
	token: string | null;
	isCheckingForToken: boolean;
	saveToken: (token: string) => Promise<void>;
	deleteToken: () => Promise<void>;
	isLoggedIn: boolean;
};

const initialValues: AuthContextType = {
	token: null,
	isCheckingForToken: true,
	saveToken: (_: string) => {
		return Promise.resolve();
	},
	deleteToken: () => {
		return Promise.resolve();
	},
	isLoggedIn: false,
};

export const AuthContext = createContext<AuthContextType>(initialValues);

type Props = {
	children: ReactNode;
};

export const AuthProvider = ({ children }: Props) => {
	const [token, setToken] = useState<string | null>(null);
	const [isCheckingForToken, setIsCheckingForToken] = useState(true);
	const isLoggedIn = !!token;

	const getToken = async () => {
		try {
			const token = await fetchFromStorage(USER_TOKEN);
			setToken(token || null);
			setIsCheckingForToken(false);
		} catch (err) {
			setToken(null);
			setIsCheckingForToken(false);
		}
	};

	const saveToken = async (token: string) => {
		try {
			await saveToStorage(USER_TOKEN, token);
			setToken(token);
		} catch (error) {
			Promise.reject(error);
		}
	};

	const deleteToken = async () => {
		try {
			await deleteFromStorage(USER_TOKEN);
			setToken(null);
		} catch (error) {
			Promise.reject(error);
		}
	};

	useEffect(() => {
		getToken();
	}, []);

	return (
		<AuthContext.Provider value={{ token, saveToken, deleteToken, isLoggedIn, isCheckingForToken }}>{children}</AuthContext.Provider>
	);
};
