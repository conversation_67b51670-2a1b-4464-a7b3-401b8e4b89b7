{"+0si3V": [{"type": 0, "value": "We automatically browse almost all real estate servers in the Czech Republic. Every day we analyze over "}, {"type": 1, "value": "count"}, {"type": 0, "value": " offers."}], "+7ZVW6": [{"type": 0, "value": "New offers from real estate servers within a minute in your inbox"}], "+IaCAS": [{"type": 0, "value": "We need few more information..."}], "+Izh69": [{"type": 0, "value": "Filter"}], "+q78O+": [{"type": 0, "value": "Lot size in m²"}], "/08LA5": [{"type": 0, "value": "Update my data"}], "/1ctzX": [{"type": 0, "value": "We promise that you email will not be abused!"}], "/LD2rR": [{"type": 0, "value": "properties"}], "/LG5zX": [{"type": 0, "value": "Registration"}], "/ge+H9": [{"type": 0, "value": "Manage your filters in your administration easily"}], "/rip8U": [{"type": 0, "value": "Register"}], "/urniO": [{"type": 0, "value": "Share on Facebook"}], "/x8dnU": [{"type": 0, "value": "Our goal is to make the tool as easy and user-friendly as possible. We summarized the functionality into four steps."}], "0K6iQs": [{"type": 1, "value": "count"}, {"type": 0, "value": " "}, {"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "minute"}]}, "other": {"value": [{"type": 0, "value": "minutes"}]}}, "pluralType": "cardinal", "type": 6, "value": "count"}, {"type": 0, "value": " reading"}], "0MffbM": [{"type": 0, "value": "most watchdogs don't send alerts until the next day?"}], "0PbWRb": [{"type": 0, "value": "How were you "}, {"children": [{"type": 0, "value": "satisfied?"}], "type": 8, "value": "strong"}], "0sHkNi": [{"type": 0, "value": "Cancel"}], "0zGipK": [{"type": 0, "value": "commercial plot"}], "1+avjK": [{"type": 0, "value": "terms and conditions"}], "1EJKJC": [{"type": 0, "value": "Show older articles"}], "1dSRzl": [{"type": 0, "value": "Contact person:"}], "1f/y/U": [{"type": 0, "value": "a property"}], "1n6hZo": [{"type": 0, "value": "On LinkedIn"}], "26EVnw": [{"type": 0, "value": "on auction"}], "2IHpVe": [{"type": 0, "value": "Your filter"}], "2LuJ3e": [{"type": 0, "value": "Searching for a well targeted advertising towards people searching for real estate properties? Advertise with us and be well seen."}], "2Ua9Pd": [{"type": 0, "value": "Dont miss out on the "}, {"children": [{"type": 0, "value": "most interesting "}, {"type": 1, "value": "br"}, {"type": 0, "value": "properties"}], "type": 8, "value": "strong"}], "35hd6X": [{"type": 0, "value": "Every "}, {"children": [{"type": 0, "value": "like"}], "type": 8, "value": "strong"}, {"type": 0, "value": " counts!"}], "3BtFTC": [{"type": 0, "value": "Price for "}, {"children": [{"type": 0, "value": "1 view is "}, {"type": 1, "value": "price"}], "type": 8, "value": "strong"}, {"type": 0, "value": "."}], "3N0UFj": [{"type": 0, "value": "Watch the latest property offers"}], "3U0efe": [{"type": 0, "value": "Helping people find new housing"}], "3cS286": [{"type": 0, "value": "New offer notification within one minute"}], "42P+nd": [{"type": 0, "value": "Choose at least one item"}], "4Dyf4j": [{"type": 0, "value": "E‑mail"}], "4TFQZM": [{"type": 0, "value": "Contact us at"}], "4b0nqS": [{"type": 0, "value": "Don't worry, enter your e-mail address and we will send you instructions."}], "4dArzd": [{"type": 0, "value": "a house"}], "4fUlFn": [{"type": 0, "value": "If you support us by sharing, we will continue to improve the service for you."}], "4kSMMM": [{"type": 0, "value": "You can cancel advertising only in written form."}], "4sbMPu": [{"type": 0, "value": "Price:"}], "4xzld0": [{"type": 0, "value": "Visidoo is a real estate offer watchdog and our users are only those interested in houses, apartments and land being sold. We offer you the possibility of  "}, {"children": [{"type": 0, "value": "very well targeted e-mail advertising"}], "type": 8, "value": "strong"}, {"type": 0, "value": " for this audience."}], "5//ob5": [{"type": 0, "value": "Support"}], "56QjZX": [{"type": 0, "value": "Price per one advertisement view:"}], "5MSxEC": [{"type": 0, "value": "This page is not available in the selected language"}], "5aQInk": [{"type": 0, "value": "Profile update"}], "5eSuqq": [{"type": 0, "value": "Choose property type"}], "5eqH9q": [{"type": 0, "value": "E-mail"}], "5xPUaq": [{"type": 0, "value": "to make an offer"}], "60AFHs": [{"type": 0, "value": "Enter your filter and just wait for new offers"}], "7+3ofS": [{"type": 0, "value": "Contacts"}], "79mG8k": [{"type": 0, "value": "I want to register"}], "7ArAk7": [{"type": 0, "value": "Adjust filter"}], "7B8qcH": [{"type": 0, "value": "utilitarian land"}], "7IvuyG": [{"type": 0, "value": "How it works"}], "7M2IY0": [{"type": 0, "value": "Roommates"}], "7PWj0N": [{"type": 0, "value": "Marketing cookies are used to track visitors across websites. The intention is to serve ads that are relevant and engaging to the individual user, and thus more valuable to publishers and third-party advertisers."}], "7QOnI4": [{"type": 0, "value": "Offers"}], "7SwnG0": [{"type": 0, "value": "You are already notified for such properties in another filter."}], "7ai6NW": [{"type": 0, "value": "Change of data"}], "7mX9pR": [{"type": 0, "value": "Don't miss out on more opportunities like this"}], "7tRq/7": [{"type": 0, "value": "We reach daily on average "}, {"children": [{"type": 1, "value": "usersReached"}, {"type": 0, "value": " unique users"}], "type": 8, "value": "strong"}, {"type": 0, "value": ", who open on average "}, {"children": [{"type": 1, "value": "emailsReceived"}, {"type": 0, "value": " e-mails"}], "type": 8, "value": "strong"}], "81rShK": [{"type": 0, "value": "My filters"}], "8kMLFO": [{"type": 0, "value": "Location:"}], "8oXBVj": [{"type": 0, "value": "On Twitter"}], "8pCD1N": [{"type": 0, "value": "Update data"}], "8pD3Tx": [{"type": 0, "value": "Expected number of sent emails"}], "96/qq3": [{"type": 0, "value": "Unfortunately something went wrong, please try again later or contact us."}], "9dHBUi": [{"type": 0, "value": "Searching for a property?"}], "9fezbz": [{"type": 0, "value": "Contact"}], "9qfKDe": [{"type": 0, "value": "<PERSON>pied"}], "9ySdzW": [{"type": 0, "value": "Watch the latest property offers"}], "A2IxuB": [{"type": 0, "value": "Land type"}], "AEb1dS": [{"type": 0, "value": "E-mail advertising"}], "ARrtJf": [{"type": 0, "value": "Time, time, time"}], "AXDHEI": [{"type": 1, "value": "count"}, {"type": 0, "value": " real estate servers"}], "AaGw8R": [{"type": 0, "value": "Add new filter"}], "AcuXDg": [{"type": 0, "value": "And we will immediately let you know about a new offer by e-mail"}], "Aljs/B": [{"type": 0, "value": "Disabled filters"}], "ArtCd3": [{"type": 0, "value": "There are new offers!"}], "Aual5W": [{"type": 1, "value": "count"}, {"type": 0, "value": " users"}], "AyFAEs": [{"type": 0, "value": "Land"}], "B0owFP": [{"type": 0, "value": "I have read and agreed with"}], "B7+LL7": [{"type": 0, "value": "We are Visidoo"}], "BFgfZ8": [{"type": 0, "value": "with "}, {"type": 1, "value": "landSize"}, {"type": 0, "value": " land"}], "BSw9Iv": [{"type": 0, "value": "commercial premises"}], "BbD63E": [{"type": 0, "value": "not all the offers are on Sreality?"}], "C9+B8X": [{"type": 0, "value": "Visidoo reserves the right to pay a deposit for the price of the advertisement before its publication, up to 100% of the price of the advertisement."}], "CZM01J": [{"type": 0, "value": "Finding suitable properties was quite time consuming, which probably everyone who is at least a little interested in real estate already knows. Watch dogs on individual real estate servers simplified their work, but they had their big pitfalls."}], "Ct3X9j": [{"type": 0, "value": "The above prices are valid from 1 January 2022."}], "D+7L36": [{"type": 0, "value": "Choose filter"}], "D36kzz": [{"type": 0, "value": "per"}], "D5IX1k": [{"type": 1, "value": "landSize"}, {"type": 0, "value": " "}, {"type": 1, "value": "landType"}, {"type": 0, "value": " "}, {"type": 1, "value": "offerType"}], "D6uoct": [{"type": 0, "value": "How it all began"}], "DEYRNk": [{"type": 0, "value": "Last found advertisements"}], "DUgm20": [{"type": 0, "value": "be roommates"}], "DXcaLq": [{"type": 0, "value": "Newest advertisements"}], "DueRdg": [{"type": 0, "value": "We write a blog about real estates"}], "Dx6FOV": [{"type": 0, "value": "You will find most of the answers quickly in"}], "E/a54p": [{"type": 0, "value": "Your password is a required field"}], "E6NpV7": [{"type": 0, "value": "Advertising"}], "EmVpu4": [{"type": 0, "value": "Sent e-mails"}], "EnM12d": [{"type": 0, "value": "Copy the filter's url"}], "F0nYR9": [{"type": 0, "value": "My filters ("}, {"type": 1, "value": "filters"}, {"type": 0, "value": ")"}], "F3joFP": [{"type": 0, "value": "monthly"}], "F6yxyL": [{"type": 0, "value": "Author:"}], "F9Et51": [{"type": 0, "value": "You always pay only for what your potential customers see."}], "FXevBd": [{"type": 0, "value": "Change of data"}], "FXwkVk": [{"type": 0, "value": "Date of last e-mail"}], "FgYJgc": [{"type": 0, "value": "Weekly we reach on average "}, {"children": [{"type": 1, "value": "weeklyUsers"}, {"type": 0, "value": " unique users"}], "type": 8, "value": "strong"}, {"type": 0, "value": ", who open on average "}, {"children": [{"type": 1, "value": "emailsNumber"}, {"type": 0, "value": " e-mails"}], "type": 8, "value": "strong"}, {"type": 0, "value": "."}], "FmXeBp": [{"type": 0, "value": "We send e-mails to "}, {"children": [{"type": 1, "value": "usersTotal"}, {"type": 0, "value": " active users"}], "type": 8, "value": "strong"}, {"type": 0, "value": ", who are currently looking for new housing. "}, {"type": 1, "value": "br"}, {"type": 0, "value": "These are those interested in buying a house, apartment or land, as well as users looking for renting an apartment or house ("}, {"children": [{"type": 0, "value": "more information"}], "type": 8, "value": "a"}, {"type": 0, "value": ")."}], "FpY61e": [{"type": 0, "value": "Be faster than others. Get notified of a newly appeared real estate offer within a minute."}], "FtAwjr": [{"type": 0, "value": "Password"}], "FtfGGu": [{"type": 0, "value": "Payment Terms"}], "G25la6": [{"type": 0, "value": "discount "}, {"type": 1, "value": "amount"}, {"type": 0, "value": "%"}], "G6Cfzg": [{"type": 0, "value": "Our story"}], "G8mRSQ": [{"type": 0, "value": "("}, {"type": 1, "value": "price"}, {"type": 0, "value": " / view)"}], "GHqPZk": [{"type": 0, "value": "to "}, {"type": 1, "value": "area"}, {"type": 0, "value": " m²"}], "GvVK9G": [{"type": 0, "value": "Prepaid advertising credit as per the advertiser's requirements"}], "HIYLhx": [{"type": 0, "value": "Published:"}], "Hjg2bW": [{"type": 0, "value": "My profile"}], "IEMJks": [{"type": 0, "value": "Agree and continue"}], "ITiok2": [{"type": 0, "value": "How it works"}], "IZyjZl": [{"type": 0, "value": "We discovered the pitfalls of watch dogs after several missed opportunities, which, unfortunately, were really worth it. How did we miss them? Despite all the effort and time spent, we were notified about them with a delay of several hours. Did you know that, for example, an apartment for rent will get the first person interested in "}, {"children": [{"type": 0, "value": "23 minutes"}], "type": 8, "value": "strong"}, {"type": 0, "value": " and usually disappears from the market "}, {"children": [{"type": 0, "value": "within seven hours"}], "type": 8, "value": "strong"}, {"type": 0, "value": "? Surprising, isn't it? We didn't know it at the time either. The more attractive the property and the better the price / performance ratio, the faster it disappears and is sold during the first showing of the property. If it's your turn the next day, you're out of luck, as the property is often reserved."}], "IpUFDX": [{"type": 0, "value": "Speed delivers results"}], "Iq3SbS": [{"type": 0, "value": "Your name is a required field"}], "IwdNnM": [{"type": 0, "value": "Email offers with advertising "}, {"children": [{"type": 0, "value": "does not end"}], "type": 8, "value": "strong"}, {"type": 0, "value": " in folders for "}, {"children": [{"type": 0, "value": "promotions"}], "type": 8, "value": "strong"}, {"type": 0, "value": " nor "}, {"children": [{"type": 0, "value": "in spam"}], "type": 8, "value": "strong"}, {"type": 0, "value": "."}], "J3GGjB": [{"type": 0, "value": "How does it work?"}], "JLoXyL": [{"type": 0, "value": "land"}], "JPrC8C": [{"type": 0, "value": "Surname"}], "JytgnK": [{"type": 0, "value": "Preference cookies allow the website to remember information that changes the way a website behaves or looks, such as your preferred language or the region you are in."}], "K9pt83": [{"type": 0, "value": "Thanks to the Visidoo service, we will send you the latest advertisement by e-mail within 1 minute. Be the first in line this time."}], "KA7GhN": [{"type": 0, "value": "Offer details"}], "Kfkw/a": [{"type": 0, "value": "Your account has been successfully deleted."}], "KkgE30": [{"type": 0, "value": "month"}], "KwH1cb": [{"type": 0, "value": "The invoice is due within 14 days from the date of its issue."}], "Kx1EWj": [{"type": 0, "value": "<PERSON><PERSON>"}], "L+eqj5": [{"type": 0, "value": "Number of rooms"}], "L52/pl": [{"type": 0, "value": "house"}], "LCa8gV": [{"type": 0, "value": "Refresh"}], "LWS3rK": [{"type": 0, "value": "Banner click"}], "M+hooz": [{"type": 0, "value": "Do you like this service?"}], "M05xeg": [{"type": 0, "value": "property"}], "M1+UGD": [{"type": 0, "value": "The probability of receiving an offer is low."}], "MBtUrI": [{"type": 0, "value": "Name"}], "MW7Ebn": [{"type": 0, "value": "See all articles"}], "MeEn5V": [{"type": 0, "value": "to the main page"}], "Mek6fT": [{"type": 0, "value": ", add your email and confirm your choice."}, {"type": 1, "value": "br"}, {"type": 0, "value": " That's all, you don't have to do more."}], "Mg3PZj": [{"type": 0, "value": "By voluntarily registering, you will get the opportunity to manage your filters in a well-arranged list."}], "MgHqxp": [{"type": 0, "value": "commercial premises"}], "Mm9lKs": [{"type": 0, "value": "New offers from real estate servers "}, {"type": 1, "value": "br"}, {"type": 0, "value": "within a minute in your inbox"}], "MuKIkW": [{"type": 0, "value": "Register"}], "N0832l": [{"type": 0, "value": "Unfortunately, we were forced to block your account because we were unable to deliver emails to you. If you no longer have access to the entered e-mail, write to us at "}, {"children": [{"type": 0, "value": "<EMAIL>"}], "type": 8, "value": "a"}, {"type": 0, "value": " and we'll try to solve it together."}], "N13Ea7": [{"type": 0, "value": "Cookies"}], "NG/WGN": [{"type": 0, "value": "Creation date"}], "NXrZju": [{"type": 0, "value": "Our values"}], "Nb24cA": [{"type": 0, "value": "Only offers with specified price"}], "NbI7Ar": [{"type": 0, "value": "Forgotten credentials"}], "NlBzuB": [{"type": 0, "value": "Detailed setup"}], "Ny2uIx": [{"children": [{"type": 0, "value": "Marketing"}], "type": 8, "value": "strong"}, {"type": 0, "value": " so you don't see ads you're not interested in"}], "NzgPRJ": [{"type": 0, "value": "Watch properties"}], "O3zZtC": [{"children": [{"type": 0, "value": "My filters"}], "type": 8, "value": "strong"}, {"type": 0, "value": " for search"}], "O9V+fO": [{"type": 0, "value": "New offers from real estate servers within a minute in your inbox"}], "OCnd1/": [{"type": 0, "value": "Edit profile"}], "ON1tET": [{"type": 0, "value": "Individual consents"}], "OmUA7k": [{"type": 0, "value": "You have been logged out"}], "OqvIDk": [{"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "new offer"}]}, "other": {"value": [{"type": 0, "value": "new offers"}]}}, "pluralType": "cardinal", "type": 6, "value": "count"}], "P7MdvT": [{"type": 0, "value": "Our advice, tips ＆ tricks"}], "P9v4mr": [{"type": 0, "value": "Billing information"}], "PGz0VW": [{"children": [{"type": 0, "value": "Necessary"}], "type": 8, "value": "strong"}, {"type": 0, "value": " for the site to work"}], "PGz8fi": [{"type": 0, "value": "On Twitter"}], "PHvm67": [{"type": 0, "value": "Type of land:"}], "Q3tfxR": [{"type": 0, "value": "Read the article"}], "QLNfoO": [{"type": 0, "value": "simple form"}], "QNH3rB": [{"type": 0, "value": "Separate room"}], "QYwPyD": [{"type": 0, "value": "We will send the latest advertisement by e-mail within 1 minute"}], "QetYa2": [{"type": 0, "value": "To change the login email ("}, {"type": 1, "value": "userEmail"}, {"type": 0, "value": ") or passwords we will send you an e-mail with further instructions:"}], "QySojM": [{"type": 0, "value": "Sale + rent, flats + houses + land, the whole Czech Republic"}], "RKuNU6": [{"type": 0, "value": "Legal matters"}], "RLnD+h": [{"type": 0, "value": "Model calculations for "}, {"children": [{"type": 0, "value": "open"}], "type": 8, "value": "strong"}, {"type": 0, "value": " e-mails"}], "RubzCy": [{"type": 0, "value": "using technology and artificial intelligence. We search most of the real estate market in the Czech Republic and inform about new properties"}], "S/uEuQ": [{"type": 0, "value": "No price information"}], "SMcfiR": [{"type": 0, "value": "<PERSON>."}], "STGTon": [{"type": 0, "value": "Change your time zone"}], "SczCcC": [{"type": 0, "value": "Essential cookies help make the website usable by enabling basic functions such as site navigation and access to secure areas of the website. Without these cookies, the website cannot function properly."}], "ShPZZR": [{"type": 0, "value": "My search filters"}], "SkCixt": [{"type": 0, "value": "All open e-mails per day"}], "SqBF1Y": [{"type": 0, "value": "per year"}], "T0abjw": [{"type": 0, "value": "We have found another more specific one for this new filter, which we have replaced for you."}], "T22fzn": [{"type": 0, "value": "Unspecified"}], "T2nOqA": [{"type": 0, "value": "Please log in to edit your filters."}], "T4HvrO": [{"type": 0, "value": "Terms and Conditions"}], "T71KYQ": [{"type": 0, "value": "E-mail position"}], "TE8+Up": [{"type": 0, "value": "For living"}], "TGRb7H": [{"type": 0, "value": "Previous page"}], "TdnzWW": [{"type": 0, "value": "Contact for advertising:"}], "TqTtq6": [{"type": 0, "value": "User"}], "U0MAxg": [{"type": 0, "value": "Yes, we can do it so quickly! We've built a unique tool to send you notifications of new ads as soon as they're published. Also check your bulk and promo folders."}], "UIhnR+": [{"type": 0, "value": "Service information"}], "UUcCPg": [{"type": 0, "value": "Don't worry, we don't send any spam"}], "UW8GHU": [{"type": 0, "value": "Delete my account"}], "UipJvA": [{"children": [{"type": 0, "value": "Preferences"}], "type": 8, "value": "strong"}, {"type": 0, "value": " to save your settings"}], "Uqp7Ag": [{"type": 0, "value": "I am looking for a property"}], "V+f9Di": [{"type": 0, "value": "We watch real estate offers for you"}], "V4iWg7": [{"type": 0, "value": "Processing of personal data"}], "VNEAls": [{"type": 0, "value": "Price"}], "Vo8+Hx": [{"type": 0, "value": "Unlike professional printed magazines, your advertisement can be "}, {"children": [{"type": 0, "value": "measured"}], "type": 8, "value": "strong"}, {"type": 0, "value": " and evaluated (impressions and clickability) with us."}], "VotR8P": [{"type": 1, "value": "count"}, {"type": 0, "value": " minutes every day"}], "Vx4/9O": [{"type": 0, "value": "Share with your family or friends. They may also find it convenient to find their dream property. At the same time, you will support our project. Thank you."}], "W3UkyP": [{"type": 0, "value": "House"}], "WHFnO4": [{"type": 0, "value": "There's been an issue while communicating with the server"}], "WVE36B": [{"type": 0, "value": "Page not found"}], "WiGBT/": [{"type": 0, "value": "All filters"}], "WosF6x": [{"type": 0, "value": "Contact seller"}], "Wu+rFJ": [{"type": 0, "value": "Unlimited"}], "WuRVZx": [{"type": 0, "value": "<PERSON><PERSON>"}], "WzuzIi": [{"type": 0, "value": "roommates"}], "X8MtFM": [{"type": 0, "value": "Czech Republic"}], "XFLoEn": [{"type": 0, "value": "Get a valuable edge over others"}], "XGjSsP": [{"type": 0, "value": "For security reasons, we will send you an email to your current address "}, {"children": [{"type": 1, "value": "email"}], "type": 8, "value": "strong"}, {"type": 0, "value": " with a unique link to change your details."}], "XIMiuP": [{"type": 0, "value": "We've sent you an email with further instructions"}], "XJ4vtx": [{"type": 0, "value": "a piece of land"}], "XSAPMI": [{"type": 0, "value": "building plot"}], "Xb5GoB": [{"type": 0, "value": "No property is waiting for you here"}], "Xr7nHW": [{"type": 0, "value": "One day we thought of how to mature the search and solve these problems at the same time. Some of us come from a technical background, where automation and programming is fun for us and our bread and butter. We started with a simple program that checked selected portals and notified us by email about new ads. Since we had an email client on our phone, like many people today, we found out  "}, {"children": [{"type": 0, "value": "about new properties on the market literally in a few minutes"}], "type": 8, "value": "strong"}, {"type": 0, "value": ". We gradually added new features, additional portals and optimized the whole program so that we didn't have to care about it. It worked like a charm and we were the first to find out about the new advertised properties and we were able to react very quickly. Sometimes the salesmen were surprised that we called them so quickly."}], "XycwOy": [{"type": 0, "value": "apartment"}], "Y8DNlS": [{"type": 0, "value": "Apartment"}], "Y9b2FY": [{"type": 0, "value": "<PERSON><PERSON>"}], "YBzJKN": [{"type": 0, "value": "For rent"}], "YZ8zM/": [{"type": 0, "value": "Name"}], "Ye0I6e": [{"type": 0, "value": "Social networks"}], "YhWnkr": [{"type": 0, "value": "or you can try to use top navigation."}], "Z+WqVP": [{"type": 0, "value": "Do you want to continue?"}], "Z/BoaB": [{"type": 0, "value": "All open e-mails per month"}], "Z0rAzw": [{"type": 0, "value": "to buy"}], "Z3idlN": [{"type": 0, "value": "Everything"}], "Z7+G+J": [{"type": 0, "value": "Search in the surrounding area"}], "ZF+7rl": [{"type": 0, "value": "(approx. "}, {"type": 1, "value": "totalUsers"}, {"type": 0, "value": " unique users / approx. "}, {"type": 1, "value": "totalEmails"}, {"type": 0, "value": " open e-mails)"}], "ZLrFeX": [{"type": 1, "value": "buildingArea"}, {"type": 0, "value": " "}, {"type": 1, "value": "disposition"}, {"type": 0, "value": " "}, {"type": 1, "value": "realEstateType"}, {"type": 0, "value": " "}, {"type": 1, "value": "landSize"}, {"type": 0, "value": " "}, {"type": 1, "value": "offerType"}], "ZsGH8n": [{"type": 0, "value": "Skip to content"}], "ZyKDDa": [{"type": 0, "value": "Contact person"}], "a2/8wN": [{"type": 0, "value": "for sale"}], "a5bE/v": [{"type": 0, "value": "E‑mail"}], "aDiC43": [{"type": 0, "value": "Cooperate with us"}], "aikxXh": [{"type": 0, "value": "The category does not contain any filters"}], "avlwNI": [{"type": 0, "value": "We place the ad in notification emails "}, {"children": [{"type": 0, "value": "to a new property offer"}], "type": 8, "value": "strong"}, {"type": 0, "value": "."}], "ayDnfe": [{"type": 0, "value": "Analytical cookies help website owners understand how visitors interact with the website by collecting and reporting information anonymously."}], "ayHGi2": [{"type": 0, "value": "Reject"}], "b4riKC": [{"type": 0, "value": "You are already receiving notifications from us for this selection."}], "bM4YaP": [{"type": 0, "value": "Share"}], "bUyPcR": [{"type": 0, "value": "Send forgotten password"}], "bZ2goH": [{"type": 0, "value": "from "}, {"type": 1, "value": "area"}, {"type": 0, "value": " m²"}], "bfmEo5": [{"type": 0, "value": "weekly"}], "bv8sQo": [{"type": 0, "value": "Sometimes we will share news from Visidoo with you"}], "c1NMT3": [{"type": 0, "value": "We are happy to expand our tool with new real estate portals or related services. "}, {"type": 1, "value": "br"}, {"type": 0, "value": "Do you think we can help each other? Write us at  "}, {"children": [{"type": 0, "value": "<EMAIL>"}], "type": 8, "value": "a"}], "cGnFjC": [{"type": 0, "value": "Notifications"}], "cHFExP": [{"type": 0, "value": "Parameters:"}], "cMN9QO": [{"type": 0, "value": "Visidoo is growing every day with new satisfied users. Join us and you will be among the first in the queue."}], "cMPVqj": [{"type": 0, "value": "Messages about your account, forgotten password etc."}], "cWfclZ": [{"type": 0, "value": "Email preferences"}], "ccuaul": [{"type": 0, "value": "After reading the story described above, you may be wondering why we want to share this competitive advantage with others. Our vision is "}, {"children": [{"type": 0, "value": "to help ordinary people to fight resellers"}], "type": 8, "value": "strong"}, {"type": 0, "value": ", who quickly buy properties with a good price tag and then sell them overpriced to people looking for housing. That is why we want to give you the options that currently have only professionals who are familiar with the real estate market and use expensive tools. We already have our own housing and we believe that thanks to our tool you will also succeed."}], "chU5KF": [{"type": 0, "value": "Real estate offers based on your filters. If you disable this option, we will have to disable all your filters."}], "cnWS4n": [{"type": 0, "value": "Your new filter is active"}], "cvMeEV": [{"type": 0, "value": "year"}], "dVyNWY": [{"type": 0, "value": "Nice to meet you"}], "dooh87": [{"type": 0, "value": "That's how much time you waste by browsing through all the property servers. We'll do it for you. Your time is precious."}], "e/uCiS": [{"type": 0, "value": "Confirm"}], "e5slVk": [{"type": 0, "value": "at"}], "e9Shdi": [{"type": 0, "value": "Confirm"}], "eKIDtn": [{"type": 0, "value": "About Visidoo"}], "eWSnHo": [{"type": 0, "value": "This website uses cookies"}], "eXdL93": [{"type": 0, "value": "Administration"}], "f5H4RU": [{"type": 0, "value": "Advertising price depends on "}, {"children": [{"type": 0, "value": "number of banner impressions"}], "type": 8, "value": "strong"}, {"type": 0, "value": "."}], "fAC3WN": [{"type": 0, "value": "DisabledFilters"}], "fD4zTL": [{"type": 0, "value": "Do you have a problem or is something not working, "}, {"type": 1, "value": "br"}, {"type": 0, "value": "or do you have an idea for a new feature?"}], "fSNmDt": [{"type": 0, "value": "Wondering why we did it or what motivated us?"}], "fWKy65": [{"type": 0, "value": "E‑mail"}], "facMHu": [{"type": 0, "value": "Send me offers without the specified price"}], "fj7+3F": [{"type": 0, "value": "is deactivated"}], "g8mE1G": [{"type": 0, "value": "on offer"}], "gCGbWG": [{"type": 0, "value": "Related articles"}], "gCdi4i": [{"type": 0, "value": "Price"}], "gJ+YOx": [{"type": 0, "value": "You probably found old link or typed in wrong addres. Don't worry, you can just return"}], "gObZRP": [{"type": 0, "value": "And because every "}, {"children": [{"type": 0, "value": "like"}], "type": 8, "value": "strong"}, {"type": 0, "value": " counts!"}], "gVoSUA": [{"type": 0, "value": "We will appreciate if you share us on your social networks. "}, {"type": 1, "value": "br"}, {"type": 0, "value": "You will support us and we can further improve our services for you. Thank you."}], "giq1Ep": [{"type": 0, "value": "Now you have a unique opportunity to respond to the ad first! Be sure to adjust the email download frequency in your email client."}], "h6O89T": [{"type": 0, "value": "Select your communication language"}], "hb+Mv2": [{"type": 0, "value": "Invalid form"}], "hehdab": [{"type": 0, "value": "Contact"}], "hhUuAD": [{"type": 0, "value": "Found"}], "hyNwsD": [{"type": 0, "value": "E-mail opening"}], "i6UnDk": [{"children": [{"type": 0, "value": "We keep an eye"}], "type": 8, "value": "strong"}, {"type": 0, "value": " for you "}, {"children": [{"type": 0, "value": "on real estate adverts"}], "type": 8, "value": "strong"}], "iD8Lqf": [{"type": 0, "value": "week"}], "iFkPyj": [{"type": 0, "value": "The primary interest of our customers"}], "iGi0+H": [{"type": 0, "value": "for rent"}], "iMxykm": [{"type": 0, "value": "Campaign period:"}], "iQg1ni": [{"type": 0, "value": "Can we ask for 2 minutes of your time to have your feedback?"}], "iRcuDX": [{"type": 0, "value": "It has been a few years since we (our friends, especially from personal life) began to look for real estate, mainly investments which were various smaller fields or land, then our own housing and then smaller investments again."}], "iZ+Vxq": [{"type": 0, "value": "utilitarian land"}], "ixRU2u": [{"type": 0, "value": "For more offers, you can more generalize the filter"}], "jP+ZY4": [{"type": 0, "value": "Blog"}], "jQgUqQ": [{"type": 0, "value": "Enter a search location"}], "kDUHF6": [{"type": 0, "value": "Land area:"}], "kh39H9": [{"type": 0, "value": "Users"}], "l2aGqu": [{"type": 0, "value": "I want "}, {"type": 1, "value": "adType"}, {"type": 0, "value": " "}, {"type": 1, "value": "realEstateType"}], "l56N6T": [{"type": 0, "value": "Delete"}], "l92l9D": [{"type": 0, "value": "Next"}], "lf1yxR": [{"type": 0, "value": "Active filters"}], "lllfkC": [{"type": 0, "value": "On LinkedIn"}], "mFEVWX": [{"type": 0, "value": "Choose your required search parameters in"}], "mx6B2u": [{"type": 0, "value": "All active filters"}], "n245M3": [{"type": 0, "value": "to auction"}], "nLVv9I": [{"type": 0, "value": "Password"}], "nPDziI": [{"type": 0, "value": "Frequently asked questions"}], "nUFcv4": [{"type": 0, "value": "Nothing"}], "nl2R+0": [{"type": 0, "value": "Accept all"}], "nlJNLx": [{"type": 0, "value": "Banner size is "}, {"children": [{"type": 0, "value": "520×250px"}], "type": 8, "value": "strong"}, {"type": 0, "value": " in the desktop version and "}, {"children": [{"type": 0, "value": "320×150px"}], "type": 8, "value": "strong"}, {"type": 0, "value": " in the mobile version."}], "o11Fyf": [{"type": 0, "value": "(The cost per impression decreases with the amount of prepaid credit)"}], "o9FuLk": [{"type": 0, "value": "approx. "}, {"children": [{"type": 1, "value": "count"}, {"type": 0, "value": " "}, {"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "property"}]}, "other": {"value": [{"type": 0, "value": "properties"}]}}, "pluralType": "cardinal", "type": 6, "value": "count"}], "type": 8, "value": "strong"}], "oWABVJ": [{"type": 0, "value": "Commercial"}], "oZgUy5": [{"type": 0, "value": "Share on Facebook"}], "oaI6jm": [{"type": 0, "value": "Your e-mail is a required field"}], "obffaC": [{"type": 0, "value": "Edit parameters"}], "of0m3c": [{"type": 0, "value": "Finish "}, {"type": 1, "value": "br"}, {"type": 0, "value": "your registration"}], "p4/SNQ": [{"type": 0, "value": "No spam and duplicate offers"}], "pBC8rw": [{"type": 0, "value": "Fill in a short questionnaire"}], "prL8MW": [{"type": 0, "value": "New password"}], "ptzSIC": [{"type": 0, "value": "to rent"}], "q7vm3r": [{"type": 0, "value": "Sale - the ratio of real estate types"}], "qBURn6": [{"type": 0, "value": "Distribution of e-mail subscribers"}], "qH18pD": [{"type": 0, "value": "You no longer need to check real estate servers. As soon as a new property appears, we will immediately notify you by e-mail."}], "qQMIV4": [{"type": 0, "value": "Edit parameters"}], "qcdxq7": [{"type": 0, "value": "Please enter an e-mail in the correct format, <EMAIL>"}], "qdVIIA": [{"type": 0, "value": "We filter out the same offers and spam from found ads."}], "r+sRDg": [{"type": 0, "value": "New offers from real estate servers "}, {"type": 1, "value": "br"}, {"type": 0, "value": "within a minute in your inbox"}], "rAL0ZK": [{"type": 0, "value": "Your filter has been deactivated!"}], "rLB+pR": [{"type": 0, "value": "To the top"}], "roeT+H": [{"type": 0, "value": "Reset password"}], "rvAAhI": [{"type": 1, "value": "count"}, {"type": 0, "value": " "}, {"offset": 0, "options": {"one": {"value": [{"type": 0, "value": "bed"}]}, "other": {"value": [{"type": 0, "value": "beds"}]}}, "pluralType": "cardinal", "type": 6, "value": "count"}], "s/wQqW": [{"type": 0, "value": "Do you need advice on setting up filters, administration or do you want to cooperate with us?"}], "s3A4hJ": [{"type": 0, "value": "frequently asked questions"}], "s46+28": [{"type": 0, "value": "Our story"}], "sJ2fdG": [{"type": 0, "value": "Don't miss "}, {"children": [{"type": 0, "value": " advice, tips ＆ tricks "}], "type": 8, "value": "strong"}, {"type": 0, "value": " from the world of Czech and foreign real estate markets"}], "sRsEfb": [{"type": 0, "value": "The price for a weekly campaign is "}, {"type": 1, "value": "price"}], "sVm2OI": [{"type": 0, "value": "Log out"}], "sWGf6W": [{"type": 0, "value": "Please enter and select a region"}], "shDLGG": [{"type": 0, "value": "Are you sure you want to delete your account? You will lose all set filters."}], "srSMJR": [{"type": 0, "value": "My profile"}], "t+oVEs": [{"children": [{"type": 0, "value": "My"}], "type": 8, "value": "strong"}, {"type": 0, "value": " profile"}], "t8YEYf": [{"type": 0, "value": "<PERSON><PERSON>"}], "tDgWau": [{"type": 0, "value": "Change login e-mail or password"}], "tW1J++": [{"type": 0, "value": "We watch real estate servers for you"}], "td1Elt": [{"type": 0, "value": "per month"}], "tebu+N": [{"type": 0, "value": "What type of advert are you interested in?"}], "trePyO": [{"type": 0, "value": "All rights reserved."}], "trqcmq": [{"children": [{"type": 0, "value": "Analytics"}], "type": 8, "value": "strong"}, {"type": 0, "value": " so that we can improve the site"}], "tsUvnz": [{"children": [{"type": 0, "value": "Your new filter"}], "type": 8, "value": "strong"}, {"type": 0, "value": " is active"}], "tzgw1D": [{"type": 0, "value": "from "}, {"type": 1, "value": "price"}], "u3ZvrZ": [{"type": 0, "value": "Number of rooms:"}], "u6w7kg": [{"type": 0, "value": "Surname"}], "u8n0Hx": [{"type": 0, "value": "Others"}], "u9XXZq": [{"type": 0, "value": "All rights reserved."}], "uF+c85": [{"type": 0, "value": "Cookies on this website are used to personalise content and ads, provide social media features and analyse traffic. In addition, we share information about your use of the website with our social media, advertising and web analytics partners, who may combine it with other information you have provided to them or that they have collected from your use of their services."}], "uHLeen": [{"type": 0, "value": "Why we do it"}], "udoQ4O": [{"type": 0, "value": "sent notifications"}], "ukSGN4": [{"type": 0, "value": "day"}], "ul/Hqr": [{"type": 0, "value": "For the most interesting offers, it's a matter of minutes."}], "uwlOdJ": [{"type": 0, "value": "Area:"}], "v7/bo2": [{"type": 0, "value": "Your e‑mail"}], "vPj+rI": [{"type": 0, "value": "Previous"}], "vQuwYf": [{"type": 0, "value": "<PERSON><PERSON>"}], "vR2ZDk": [{"type": 0, "value": "Don't waste your time browsing many different servers."}], "vYQ+8d": [{"type": 0, "value": "We believe that good service can be free and without registration."}], "vbDvx5": [{"type": 0, "value": "All open e-mails per week"}], "vlEMRD": [{"type": 0, "value": "building plot"}], "vrkx6m": [{"type": 0, "value": "Free and without registration"}], "vvadPR": [{"type": 0, "value": "the fastest on the market"}], "w4XR8f": [{"type": 0, "value": "commercial plot"}], "wBW/Rt": [{"type": 0, "value": "an apartment"}], "wDssP1": [{"type": 0, "value": "Winter sale"}], "wEKWKy": [{"type": 0, "value": "We are Visidoo, we help people find housing."}], "wLoXnk": [{"type": 0, "value": "For more offers, you can "}, {"children": [{"type": 0, "value": "generalize the filter"}], "type": 8, "value": "a"}], "wR+Uu6": [{"type": 0, "value": "Go to seller's website"}], "wvB+IM": [{"type": 0, "value": "Enter your e-mail and we will send you further instructions."}], "wvHZvh": [{"type": 0, "value": "Learn more"}], "wzNL2Q": [{"type": 0, "value": "to "}, {"type": 1, "value": "price"}], "x/v8KP": [{"type": 0, "value": "Automation as the solution"}], "xG32K2": [{"type": 0, "value": "Change the details"}], "xKuOwZ": [{"type": 0, "value": "New e‑mail"}], "xVQ2ur": [{"type": 0, "value": "For sale"}], "xekV/w": [{"type": 0, "value": "Don't spend hours looking for properties. We will do it for you."}], "xo5yZc": [{"type": 0, "value": "Visidoo is "}, {"children": [{"type": 0, "value": "free"}], "type": 8, "value": "strong"}, {"type": 0, "value": ", without registration "}, {"type": 1, "value": "br"}, {"type": 0, "value": "and without annoying spam"}], "xoGz1H": [{"type": 0, "value": "Rent - the ratio of types of real estate"}], "y4qZ+8": [{"children": [{"type": 0, "value": " Did you like this article? "}], "type": 8, "value": "span"}, {"type": 0, "value": " Support us and share it with your friends."}], "yBNW23": [{"type": 0, "value": "Go to the main menu"}], "yEwqht": [{"type": 0, "value": "Area in m²"}], "yfXzq2": [{"type": 0, "value": "Skip to content (Keyboard shortcut: Alt + 2)"}], "yoRHa2": [{"type": 0, "value": "Next page"}], "yyque6": [{"type": 0, "value": "daily"}], "z2HVN7": [{"type": 0, "value": "Enter the region, district, municipality…"}], "zArO3x": [{"type": 0, "value": "Don't miss out on the most interesting properties"}], "zI5ps/": [{"type": 0, "value": "Newsletters"}], "zN5xf7": [{"type": 0, "value": "Don't miss out on "}, {"children": [{"type": 0, "value": "more opportunities"}], "type": 8, "value": "strong"}, {"type": 0, "value": " "}, {"type": 1, "value": "br"}, {"type": 0, "value": "like this"}], "zagtsA": [{"type": 0, "value": "Probability of receiving a notification "}, {"children": [{"type": 0, "value": "is low"}], "type": 8, "value": "strong"}], "zb1ysM": [{"type": 0, "value": "Your surname is a required field"}], "ziJOTt": [{"type": 0, "value": "How it works"}]}